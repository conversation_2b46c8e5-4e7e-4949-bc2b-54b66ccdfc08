#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
续杯工具 - 纯本地界面启动器
不需要服务器，直接使用本地HTML文件和pywebview的JS API
"""

import sys
import os
import multiprocessing

# 设置控制台编码为UTF-8，避免emoji显示问题
if sys.platform == 'win32':
    try:
        # 尝试设置控制台为UTF-8编码
        os.system('chcp 65001 >nul 2>&1')
        # 重新配置stdout编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
    except:
        # 如果设置失败，使用安全的打印函数
        pass

def safe_print(text):
    """安全的打印函数，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果遇到编码错误，移除emoji字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

def main():
    """主函数"""
    try:
        safe_print("🚀 启动续杯工具 - 纯本地界面版")
        safe_print("📋 这个版本不需要服务器，完全本地运行")

        # 检查依赖
        try:
            import webview
            safe_print("✅ pywebview 依赖检查通过")
        except ImportError:
            safe_print("❌ 缺少 pywebview 依赖")
            safe_print("📦 请运行: pip install pywebview")
            input("按回车键退出...")
            return
        
        # 导入本地界面管理器
        try:
            from 本地界面.本地界面管理器 import create_local_gui
            safe_print("✅ 本地界面管理器导入成功")
        except ImportError as e:
            safe_print(f"❌ 本地界面管理器导入失败: {e}")
            safe_print("📁 请确保 '本地界面' 文件夹存在且包含必要文件")
            input("按回车键退出...")
            return

        # 启动本地界面
        safe_print("🖥️ 正在启动本地界面...")
        if create_local_gui():
            safe_print("✅ 界面启动成功")
        else:
            safe_print("❌ 界面启动失败")
            input("按回车键退出...")

    except KeyboardInterrupt:
        safe_print("\n👋 用户取消，程序退出")
    except Exception as e:
        safe_print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    # 支持多进程打包
    multiprocessing.freeze_support()
    main()
