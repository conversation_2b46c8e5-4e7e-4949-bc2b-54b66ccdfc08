package email

import (
	"crypto/tls"
	"fmt"
	"net"
	"regexp"
	"strings"
	"time"

	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"
)

// Client represents an email client for receiving emails
type Client struct {
	host     string
	port     int
	username string
	password string
	logger   *logger.Logger
}

// NewClient creates a new email client
func NewClient(host string, port int, username, password string) *Client {
	return &Client{
		host:     host,
		port:     port,
		username: username,
		password: password,
		logger:   logger.Get(),
	}
}

// NewPOP3Client creates a new POP3 email client for 2925.com
func NewPOP3Client(username, password string) *Client {
	return NewClient("pop.2925.com", 995, username, password)
}

// Connect establishes a connection to the email server
func (c *Client) Connect() (net.Conn, error) {
	address := fmt.Sprintf("%s:%d", c.host, c.port)
	c.logger.Infof("Connecting to %s...", address)

	// Create TLS connection
	conn, err := tls.Dial("tcp", address, &tls.Config{
		ServerName:         c.host,
		InsecureSkipVerify: true, // For development/testing
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", address, err)
	}

	c.logger.Info(utils.FormatSuccess("Connected to email server"))
	return conn, nil
}

// Authenticate performs POP3 authentication
func (c *Client) Authenticate(conn net.Conn) error {
	c.logger.Info("Authenticating with email server...")

	// Read greeting
	if err := c.readResponse(conn, "+OK"); err != nil {
		return fmt.Errorf("failed to read greeting: %w", err)
	}

	// Send USER command
	if err := c.sendCommand(conn, fmt.Sprintf("USER %s", c.username)); err != nil {
		return fmt.Errorf("failed to send USER command: %w", err)
	}

	if err := c.readResponse(conn, "+OK"); err != nil {
		return fmt.Errorf("USER command failed: %w", err)
	}

	// Send PASS command
	if err := c.sendCommand(conn, fmt.Sprintf("PASS %s", c.password)); err != nil {
		return fmt.Errorf("failed to send PASS command: %w", err)
	}

	if err := c.readResponse(conn, "+OK"); err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	c.logger.Info(utils.FormatSuccess("Authentication successful"))
	return nil
}

// GetMessageCount returns the number of messages in the mailbox
func (c *Client) GetMessageCount(conn net.Conn) (int, error) {
	if err := c.sendCommand(conn, "STAT"); err != nil {
		return 0, fmt.Errorf("failed to send STAT command: %w", err)
	}

	response, err := c.readResponseLine(conn)
	if err != nil {
		return 0, fmt.Errorf("failed to read STAT response: %w", err)
	}

	var count int
	if _, err := fmt.Sscanf(response, "+OK %d", &count); err != nil {
		return 0, fmt.Errorf("failed to parse message count: %w", err)
	}

	return count, nil
}

// RetrieveMessage retrieves a specific message by number
func (c *Client) RetrieveMessage(conn net.Conn, messageNum int) (string, error) {
	if err := c.sendCommand(conn, fmt.Sprintf("RETR %d", messageNum)); err != nil {
		return "", fmt.Errorf("failed to send RETR command: %w", err)
	}

	if err := c.readResponse(conn, "+OK"); err != nil {
		return "", fmt.Errorf("RETR command failed: %w", err)
	}

	// Read message content until "."
	var message strings.Builder
	buffer := make([]byte, 1024)

	for {
		conn.SetReadDeadline(time.Now().Add(10 * time.Second))
		n, err := conn.Read(buffer)
		if err != nil {
			return "", fmt.Errorf("failed to read message: %w", err)
		}

		content := string(buffer[:n])
		message.WriteString(content)

		// Check for end of message marker
		if strings.Contains(content, "\r\n.\r\n") {
			break
		}
	}

	return message.String(), nil
}

// ExtractVerificationCode extracts verification code from email content
func (c *Client) ExtractVerificationCode(content string) (string, error) {
	// Common patterns for verification codes
	patterns := []string{
		`(?i)verification\s+code[:\s]+([0-9]{4,8})`,
		`(?i)code[:\s]+([0-9]{4,8})`,
		`(?i)your\s+code[:\s]+([0-9]{4,8})`,
		`(?i)([0-9]{6})`, // 6-digit codes are common
		`(?i)([0-9]{4})`, // 4-digit codes
		`(?i)([0-9]{8})`, // 8-digit codes
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			code := strings.TrimSpace(matches[1])
			if len(code) >= 4 && len(code) <= 8 {
				c.logger.Infof("Found verification code: %s", code)
				return code, nil
			}
		}
	}

	return "", fmt.Errorf("no verification code found in email content")
}

// DeleteMessage deletes a message from the server
func (c *Client) DeleteMessage(conn net.Conn, messageNum int) error {
	if err := c.sendCommand(conn, fmt.Sprintf("DELE %d", messageNum)); err != nil {
		return fmt.Errorf("failed to send DELE command: %w", err)
	}

	if err := c.readResponse(conn, "+OK"); err != nil {
		return fmt.Errorf("DELE command failed: %w", err)
	}

	return nil
}

// Quit closes the connection gracefully
func (c *Client) Quit(conn net.Conn) error {
	if err := c.sendCommand(conn, "QUIT"); err != nil {
		return fmt.Errorf("failed to send QUIT command: %w", err)
	}

	if err := c.readResponse(conn, "+OK"); err != nil {
		return fmt.Errorf("QUIT command failed: %w", err)
	}

	return conn.Close()
}

// sendCommand sends a command to the server
func (c *Client) sendCommand(conn net.Conn, command string) error {
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	_, err := conn.Write([]byte(command + "\r\n"))
	return err
}

// readResponse reads and validates a response from the server
func (c *Client) readResponse(conn net.Conn, expectedPrefix string) error {
	response, err := c.readResponseLine(conn)
	if err != nil {
		return err
	}

	if !strings.HasPrefix(response, expectedPrefix) {
		return fmt.Errorf("unexpected response: %s", response)
	}

	return nil
}

// readResponseLine reads a single line response from the server
func (c *Client) readResponseLine(conn net.Conn) (string, error) {
	buffer := make([]byte, 1024)
	var response strings.Builder

	conn.SetReadDeadline(time.Now().Add(10 * time.Second))

	for {
		n, err := conn.Read(buffer)
		if err != nil {
			return "", err
		}

		content := string(buffer[:n])
		response.WriteString(content)

		// Check for end of line
		if strings.Contains(content, "\r\n") {
			break
		}
	}

	line := strings.Split(response.String(), "\r\n")[0]
	return line, nil
}

// TestConnection tests the email connection and authentication
func (c *Client) TestConnection() error {
	c.logger.Info("Testing email connection...")

	conn, err := c.Connect()
	if err != nil {
		return fmt.Errorf("connection test failed: %w", err)
	}
	defer conn.Close()

	if err := c.Authenticate(conn); err != nil {
		return fmt.Errorf("authentication test failed: %w", err)
	}

	count, err := c.GetMessageCount(conn)
	if err != nil {
		return fmt.Errorf("failed to get message count: %w", err)
	}

	c.logger.Infof("Connection test successful. Messages in mailbox: %d", count)

	if err := c.Quit(conn); err != nil {
		c.logger.Warnf("Failed to quit gracefully: %v", err)
	}

	return nil
}
