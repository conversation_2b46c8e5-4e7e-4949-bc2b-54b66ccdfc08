// Cursor Manager Go - Frontend JavaScript

class CursorManagerApp {
    constructor() {
        this.apiBase = '/api/v1';
        this.ws = null;
        this.config = {};
        this.init();
    }

    async init() {
        console.log('🚀 Initializing Cursor Manager Go');
        
        // Load configuration
        await this.loadConfig();
        
        // Generate initial email
        await this.generateEmail();
        
        // Setup WebSocket connection
        this.setupWebSocket();
        
        // Setup auto-refresh for email
        this.setupEmailAutoRefresh();
        
        // Update connection status
        this.updateConnectionStatus(true);
        
        this.addStatusMessage('应用程序初始化完成', 'success');
    }

    // API Methods
    async apiCall(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'API call failed');
            }
            
            return data;
        } catch (error) {
            console.error('API call failed:', error);
            this.showNotification(error.message, 'error');
            throw error;
        }
    }

    // Configuration Methods
    async loadConfig() {
        try {
            const response = await this.apiCall('/config');
            this.config = response.data;
            
            // Update UI with config values
            document.getElementById('emailPrefix').value = this.config.email_prefix || '';
            document.getElementById('emailPassword').value = this.config.email_password || '';
            document.getElementById('browserPath').value = this.config.browser_path || '';
            document.getElementById('headlessMode').checked = this.config.browser_headless || false;
            
            this.addStatusMessage('配置加载成功', 'success');
        } catch (error) {
            this.addStatusMessage('配置加载失败: ' + error.message, 'error');
        }
    }

    async saveConfig(configData) {
        try {
            await this.apiCall('/config', {
                method: 'POST',
                body: JSON.stringify(configData),
            });
            
            // Update local config
            Object.assign(this.config, configData);
            
            this.addStatusMessage('配置保存成功', 'success');
            this.showNotification('配置已保存', 'success');
            
            return true;
        } catch (error) {
            this.addStatusMessage('配置保存失败: ' + error.message, 'error');
            return false;
        }
    }

    // Email Methods
    async generateEmail() {
        try {
            const response = await this.apiCall('/email/generate');
            const email = response.data.email;
            
            document.getElementById('currentEmail').value = email;
            this.addStatusMessage(`生成新邮箱: ${email}`, 'info');
            
            return email;
        } catch (error) {
            this.addStatusMessage('生成邮箱失败: ' + error.message, 'error');
            return null;
        }
    }

    async testEmailConnection() {
        const email = this.getMonitoringEmail();
        const password = document.getElementById('emailPassword').value;
        
        if (!email || !password) {
            this.showNotification('请先设置邮箱前缀和密码', 'warning');
            return;
        }

        this.showLoading(true);
        
        try {
            await this.apiCall('/email/test', {
                method: 'POST',
                body: JSON.stringify({ email, password }),
            });
            
            this.showNotification('邮箱连接测试成功', 'success');
            this.addStatusMessage('邮箱连接测试通过', 'success');
        } catch (error) {
            this.showNotification('邮箱连接测试失败', 'error');
            this.addStatusMessage('邮箱连接测试失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async startEmailMonitoring() {
        const email = this.getMonitoringEmail();
        const password = document.getElementById('emailPassword').value;
        
        if (!email || !password) {
            this.showNotification('请先设置邮箱前缀和密码', 'warning');
            return;
        }

        try {
            await this.apiCall('/email/monitor/start', {
                method: 'POST',
                body: JSON.stringify({ email, password }),
            });
            
            this.showNotification('邮箱监控已启动', 'success');
            this.addStatusMessage(`邮箱监控已启动: ${email}`, 'success');
        } catch (error) {
            this.showNotification('启动邮箱监控失败', 'error');
            this.addStatusMessage('启动邮箱监控失败: ' + error.message, 'error');
        }
    }

    // Browser Automation Methods
    async autoLogin() {
        const targetUrl = document.getElementById('targetUrl').value;
        const loginEmail = document.getElementById('currentEmail').value;
        const monitoringEmail = this.getMonitoringEmail();
        const password = document.getElementById('emailPassword').value;
        
        if (!targetUrl) {
            this.showNotification('请输入目标URL', 'warning');
            return;
        }
        
        if (!loginEmail || !monitoringEmail || !password) {
            this.showNotification('请先完成邮箱设置', 'warning');
            return;
        }

        this.showLoading(true);
        this.addStatusMessage('开始一键登录流程...', 'info');
        
        try {
            const response = await this.apiCall('/browser/login', {
                method: 'POST',
                body: JSON.stringify({
                    target_url: targetUrl,
                    login_email: loginEmail,
                    monitoring_email: monitoringEmail,
                    password: password,
                }),
            });
            
            this.addStatusMessage('一键登录完成', 'success');
            this.showNotification('一键登录完成', 'success');
            
            if (response.data.final_url) {
                this.addStatusMessage(`最终URL: ${response.data.final_url}`, 'info');
            }
        } catch (error) {
            this.addStatusMessage('一键登录失败: ' + error.message, 'error');
            this.showNotification('一键登录失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async quickLogin() {
        const targetUrl = document.getElementById('targetUrl').value;
        const email = document.getElementById('currentEmail').value;
        
        if (!targetUrl || !email) {
            this.showNotification('请输入目标URL和邮箱', 'warning');
            return;
        }

        this.showLoading(true);
        
        try {
            await this.apiCall('/browser/quick-login', {
                method: 'POST',
                body: JSON.stringify({
                    target_url: targetUrl,
                    email: email,
                }),
            });
            
            this.addStatusMessage('快速登录完成，请手动继续', 'success');
            this.showNotification('快速登录完成', 'success');
        } catch (error) {
            this.addStatusMessage('快速登录失败: ' + error.message, 'error');
            this.showNotification('快速登录失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Cursor Management Methods
    async resetEnvironment() {
        if (!confirm('确定要重置Cursor环境吗？这将关闭Cursor进程并重置机器ID。')) {
            return;
        }

        this.showLoading(true);
        this.addStatusMessage('开始环境重置...', 'info');
        
        try {
            await this.apiCall('/cursor/reset', {
                method: 'POST',
            });
            
            this.addStatusMessage('环境重置已启动', 'success');
            this.showNotification('环境重置已启动', 'success');
        } catch (error) {
            this.addStatusMessage('环境重置失败: ' + error.message, 'error');
            this.showNotification('环境重置失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async restartCursor() {
        this.showLoading(true);
        this.addStatusMessage('重启Cursor...', 'info');
        
        try {
            await this.apiCall('/cursor/restart', {
                method: 'POST',
            });
            
            this.addStatusMessage('Cursor重启已启动', 'success');
            this.showNotification('Cursor重启已启动', 'success');
        } catch (error) {
            this.addStatusMessage('Cursor重启失败: ' + error.message, 'error');
            this.showNotification('Cursor重启失败', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async getCursorStatus() {
        try {
            const response = await this.apiCall('/cursor/status');
            const status = response.data;
            
            this.showModal('Cursor状态', this.formatCursorStatus(status));
        } catch (error) {
            this.showNotification('获取状态失败', 'error');
        }
    }

    // Utility Methods
    getMonitoringEmail() {
        const prefix = this.config.email_prefix || document.getElementById('emailPrefix').value;
        const domain = this.config.email_domain || '2925.com';
        return prefix ? `${prefix}@${domain}` : '';
    }

    formatCursorStatus(status) {
        return `
            <div class="status-info">
                <p><strong>运行状态:</strong> ${status.is_running ? '运行中' : '未运行'}</p>
                <p><strong>路径有效:</strong> ${status.paths_valid ? '是' : '否'}</p>
                <p><strong>数据库存在:</strong> ${status.database_exists ? '是' : '否'}</p>
                <p><strong>机器ID文件存在:</strong> ${status.machine_id_exists ? '是' : '否'}</p>
                <p><strong>数据路径:</strong> ${status.paths?.data_path || 'N/A'}</p>
                <p><strong>应用路径:</strong> ${status.paths?.app_path || 'N/A'}</p>
            </div>
        `;
    }

    setupEmailAutoRefresh() {
        // Auto-refresh email every 30 seconds
        setInterval(() => {
            if (this.config.email_prefix) {
                this.generateEmail();
            }
        }, 30000);
    }

    setupWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/api/v1/ws`;
        
        try {
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.updateConnectionStatus(true);
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('WebSocket message parse error:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket disconnected');
                this.updateConnectionStatus(false);
                
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    this.setupWebSocket();
                }, 5000);
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
        } catch (error) {
            console.error('WebSocket setup failed:', error);
            this.updateConnectionStatus(false);
        }
    }

    handleWebSocketMessage(data) {
        if (data.type === 'status') {
            this.addStatusMessage(data.message, data.level || 'info');
        } else if (data.type === 'verification_code') {
            this.addStatusMessage(`收到验证码: ${data.code}`, 'success');
            this.showNotification(`验证码: ${data.code}`, 'success');
        }
    }

    updateConnectionStatus(connected) {
        const statusIndicator = document.getElementById('connectionStatus');
        const statusDot = statusIndicator.querySelector('.status-dot');
        const statusText = statusIndicator.querySelector('.status-text');
        
        if (connected) {
            statusDot.style.background = '#4ade80';
            statusText.textContent = '已连接';
        } else {
            statusDot.style.background = '#ef4444';
            statusText.textContent = '连接断开';
        }
    }

    // UI Helper Methods
    addStatusMessage(message, type = 'info') {
        const statusContent = document.getElementById('statusContent');
        const timestamp = new Date().toLocaleTimeString();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `status-message ${type}`;
        messageDiv.innerHTML = `
            <span class="timestamp">${timestamp}</span>
            <span class="message">${message}</span>
        `;
        
        statusContent.appendChild(messageDiv);
        statusContent.scrollTop = statusContent.scrollHeight;
        
        // Limit to 100 messages
        while (statusContent.children.length > 100) {
            statusContent.removeChild(statusContent.firstChild);
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span class="emoji">${this.getTypeEmoji(type)}</span>
            <span>${message}</span>
        `;
        
        container.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    getTypeEmoji(type) {
        const emojis = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return emojis[type] || 'ℹ️';
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.add('show');
        } else {
            overlay.classList.remove('show');
        }
    }

    showModal(title, content) {
        const overlay = document.getElementById('modalOverlay');
        const titleElement = document.getElementById('modalTitle');
        const contentElement = document.getElementById('modalContent');
        
        titleElement.textContent = title;
        contentElement.innerHTML = content;
        overlay.classList.add('show');
    }

    closeModal() {
        const overlay = document.getElementById('modalOverlay');
        overlay.classList.remove('show');
    }

    clearStatus() {
        const statusContent = document.getElementById('statusContent');
        statusContent.innerHTML = '';
        this.addStatusMessage('状态日志已清空', 'info');
    }

    exportLogs() {
        const statusContent = document.getElementById('statusContent');
        const messages = Array.from(statusContent.children).map(msg => {
            const timestamp = msg.querySelector('.timestamp').textContent;
            const message = msg.querySelector('.message').textContent;
            return `${timestamp} ${message}`;
        }).join('\n');
        
        const blob = new Blob([messages], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cursor-manager-logs-${new Date().toISOString().slice(0, 10)}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Global functions for HTML onclick handlers
let app;

document.addEventListener('DOMContentLoaded', () => {
    app = new CursorManagerApp();
});

// Global functions
function copyEmail() {
    const emailInput = document.getElementById('currentEmail');
    emailInput.select();
    document.execCommand('copy');
    app.showNotification('邮箱已复制到剪贴板', 'success');
}

function autoLogin() {
    app.autoLogin();
}

function quickLogin() {
    app.quickLogin();
}

function saveEmailConfig() {
    const prefix = document.getElementById('emailPrefix').value;
    const password = document.getElementById('emailPassword').value;
    
    if (!prefix || !password) {
        app.showNotification('请填写邮箱前缀和密码', 'warning');
        return;
    }
    
    app.saveConfig({
        email_prefix: prefix,
        email_password: password,
    });
}

function testEmailConnection() {
    app.testEmailConnection();
}

function saveBrowserConfig() {
    const browserPath = document.getElementById('browserPath').value;
    const headless = document.getElementById('headlessMode').checked;
    
    app.saveConfig({
        browser_path: browserPath,
        browser_headless: headless,
    });
}

function resetEnvironment() {
    app.resetEnvironment();
}

function restartCursor() {
    app.restartCursor();
}

function startEmailMonitoring() {
    app.startEmailMonitoring();
}

function getCursorStatus() {
    app.getCursorStatus();
}

function clearStatus() {
    app.clearStatus();
}

function exportLogs() {
    app.exportLogs();
}

function closeModal() {
    app.closeModal();
}

function showSystemInfo() {
    fetch('/api/v1/system/info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const info = data.data;
                const content = `
                    <div class="system-info">
                        <p><strong>操作系统:</strong> ${info.os} ${info.os_version}</p>
                        <p><strong>架构:</strong> ${info.arch}</p>
                        <p><strong>Go版本:</strong> ${info.go_version}</p>
                        <p><strong>管理员权限:</strong> ${info.is_admin ? '是' : '否'}</p>
                        <p><strong>CPU核心数:</strong> ${info.num_cpu}</p>
                        <p><strong>Goroutine数:</strong> ${info.num_goroutine}</p>
                    </div>
                `;
                app.showModal('系统信息', content);
            }
        })
        .catch(error => {
            app.showNotification('获取系统信息失败', 'error');
        });
}

function showHelp() {
    const helpContent = `
        <div class="help-content">
            <h4>使用说明</h4>
            <ol>
                <li><strong>邮箱设置:</strong> 设置邮箱前缀和密码，用于接收验证码</li>
                <li><strong>一键登录:</strong> 输入目标URL，自动完成登录流程</li>
                <li><strong>快速登录:</strong> 只填写邮箱，需要手动完成后续步骤</li>
                <li><strong>环境重置:</strong> 重置Cursor机器ID和环境</li>
                <li><strong>邮箱监控:</strong> 监控邮箱接收验证码</li>
            </ol>
            <h4>注意事项</h4>
            <ul>
                <li>首次使用请先配置邮箱设置</li>
                <li>环境重置需要管理员权限</li>
                <li>请确保Chrome浏览器已正确安装</li>
            </ul>
        </div>
    `;
    app.showModal('帮助', helpContent);
}

function showAbout() {
    const aboutContent = `
        <div class="about-content">
            <h4>关于 Cursor Manager Go</h4>
            <p>Cursor Manager Go 是一个现代化的Cursor编辑器管理工具，使用Go语言重构。</p>
            <h4>主要功能</h4>
            <ul>
                <li>🚀 自动化登录流程</li>
                <li>🔄 环境重置和机器ID管理</li>
                <li>📧 邮箱验证码监控</li>
                <li>🌐 现代化Web界面</li>
                <li>⚡ 高性能和跨平台支持</li>
            </ul>
            <h4>技术栈</h4>
            <p>后端: Go + Gin + ChromeDP + SQLite</p>
            <p>前端: HTML5 + CSS3 + JavaScript + WebSocket</p>
            <p class="version">版本: v1.0.0</p>
        </div>
    `;
    app.showModal('关于', aboutContent);
}
