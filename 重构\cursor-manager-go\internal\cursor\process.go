package cursor

import (
	"fmt"
	"runtime"
	"time"

	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"
)

// ProcessManager handles Cursor process operations
type ProcessManager struct {
	paths  *Paths
	logger *logger.Logger
}

// NewProcessManager creates a new ProcessManager
func NewProcessManager() (*ProcessManager, error) {
	paths, err := GetCursorPaths()
	if err != nil {
		return nil, fmt.Errorf("failed to get Cursor paths: %w", err)
	}

	return &ProcessManager{
		paths:  paths,
		logger: logger.Get(),
	}, nil
}

// TerminateAll terminates all Cursor-related processes
func (pm *ProcessManager) TerminateAll() (int, error) {
	pm.logger.Infof("%s Detected operating system: %s", utils.GetEmoji("computer"), runtime.GOOS)
	pm.logger.Info(utils.FormatInfo("Starting scan and termination of Cursor-related processes..."))

	processNames := pm.getCursorProcessNames()
	pm.logger.Infof("%s %s system - will check the following processes:", utils.GetEmoji("info"), runtime.GOOS)
	
	for i, name := range processNames {
		pm.logger.Infof("   %d. %s", i+1, name)
	}
	pm.logger.Info("")

	processesKilled := 0
	processesChecked := 0

	for _, name := range processNames {
		processesChecked++
		pm.logger.Infof("%s [%d/%d] Checking process: %s", utils.GetEmoji("gear"), processesChecked, len(processNames), name)

		pids, err := utils.FindProcess(name)
		if err != nil {
			pm.logger.Warnf("   %s Warning: Failed to find process '%s': %v", utils.GetEmoji("warning"), name, err)
			continue
		}

		if len(pids) == 0 {
			pm.logger.Infof("   %s Process not running: %s", utils.GetEmoji("info"), name)
			continue
		}

		pm.logger.Infof("   %s Found %d instance(s) of process: %s", utils.GetEmoji("target"), len(pids), name)

		if err := utils.KillProcess(name); err != nil {
			pm.logger.Warnf("   %s Warning: Failed to terminate '%s': %v", utils.GetEmoji("warning"), name, err)
		} else {
			pm.logger.Infof("   %s Successfully terminated process: %s", utils.GetEmoji("success"), name)
			processesKilled++
		}
	}

	// Final summary
	pm.logger.Info("")
	pm.logger.Info(utils.FormatInfo("Process termination summary:"))
	pm.logger.Infof("   %s Processes checked: %d", utils.GetEmoji("gear"), processesChecked)
	pm.logger.Infof("   %s Processes terminated: %d", utils.GetEmoji("success"), processesKilled)
	pm.logger.Infof("   %s Processes not found: %d", utils.GetEmoji("info"), processesChecked-processesKilled)

	if processesKilled > 0 {
		pm.logger.Infof("%s Operation completed: Terminated %d Cursor-related processes", utils.GetEmoji("target"), processesKilled)
	} else {
		pm.logger.Info(utils.FormatInfo("No running Cursor-related processes found"))
	}

	return processesKilled, nil
}

// Launch starts the Cursor application
func (pm *ProcessManager) Launch() error {
	pm.logger.Info(utils.FormatInfo("Attempting to launch Cursor application..."))

	pm.logger.Info(utils.FormatInfo("Getting Cursor installation path..."))
	pm.logger.Infof("%s Found Cursor path: %s", utils.GetEmoji("folder"), pm.paths.AppPath)

	// Check if file exists
	if !utils.FileExists(pm.paths.AppPath) {
		pm.logger.Error(utils.FormatError("Cursor application file does not exist"))
		pm.logger.Infof("%s Expected path: %s", utils.GetEmoji("folder"), pm.paths.AppPath)
		pm.logger.Info(utils.FormatInfo("Suggestion: Please check if Cursor is properly installed"))
		return fmt.Errorf("cursor executable not found: %s", pm.paths.AppPath)
	}

	pm.logger.Info(utils.FormatSuccess("Application file exists, preparing to launch..."))

	if err := utils.LaunchApplication(pm.paths.AppPath); err != nil {
		pm.logger.Error(utils.FormatError("Failed to launch Cursor: " + err.Error()))
		return fmt.Errorf("failed to launch Cursor: %w", err)
	}

	pm.logger.Info(utils.FormatSuccess("Successfully sent launch command to Cursor"))
	pm.logger.Info(utils.FormatLoading("Please wait for Cursor application to start..."))

	return nil
}

// IsRunning checks if Cursor is currently running
func (pm *ProcessManager) IsRunning() (bool, error) {
	processNames := pm.getCursorProcessNames()
	
	for _, name := range processNames {
		pids, err := utils.FindProcess(name)
		if err != nil {
			continue // Ignore errors and check next process
		}
		
		if len(pids) > 0 {
			return true, nil
		}
	}
	
	return false, nil
}

// WaitForTermination waits for all Cursor processes to terminate
func (pm *ProcessManager) WaitForTermination(timeout time.Duration) error {
	pm.logger.Infof("%s Waiting for Cursor processes to terminate (timeout: %v)...", utils.GetEmoji("timer"), timeout)
	
	start := time.Now()
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			running, err := pm.IsRunning()
			if err != nil {
				return fmt.Errorf("failed to check if Cursor is running: %w", err)
			}
			
			if !running {
				elapsed := time.Since(start)
				pm.logger.Infof("%s All Cursor processes terminated (took %v)", utils.GetEmoji("success"), elapsed)
				return nil
			}
			
			if time.Since(start) >= timeout {
				return fmt.Errorf("timeout waiting for Cursor processes to terminate")
			}
			
		case <-time.After(timeout):
			return fmt.Errorf("timeout waiting for Cursor processes to terminate")
		}
	}
}

// getCursorProcessNames returns the list of Cursor process names for the current OS
func (pm *ProcessManager) getCursorProcessNames() []string {
	switch runtime.GOOS {
	case "windows":
		return []string{
			"Cursor.exe",
			"cursor.exe",
			"Cursor Helper.exe",
			"cursor-updater.exe",
		}
	case "darwin":
		return []string{
			"Cursor",
			"Cursor Helper",
			"com.todesktop.230313mzl4w4u92", // Cursor's bundle identifier
		}
	case "linux":
		return []string{
			"cursor",
			"cursor-url-handler",
			"cursor-crashpad-handler",
		}
	default:
		return []string{"cursor"}
	}
}

// GetProcessInfo returns information about running Cursor processes
func (pm *ProcessManager) GetProcessInfo() (map[string][]int, error) {
	processNames := pm.getCursorProcessNames()
	processInfo := make(map[string][]int)
	
	for _, name := range processNames {
		pids, err := utils.FindProcess(name)
		if err != nil {
			continue // Ignore errors and continue with next process
		}
		
		if len(pids) > 0 {
			processInfo[name] = pids
		}
	}
	
	return processInfo, nil
}

// ForceKill forcefully kills all Cursor processes (more aggressive than TerminateAll)
func (pm *ProcessManager) ForceKill() error {
	pm.logger.Warn(utils.FormatWarning("Force killing all Cursor processes..."))
	
	processNames := pm.getCursorProcessNames()
	
	for _, name := range processNames {
		if err := utils.KillProcess(name); err != nil {
			pm.logger.Warnf("Failed to force kill process %s: %v", name, err)
		} else {
			pm.logger.Infof("Force killed process: %s", name)
		}
	}
	
	// Wait a moment for processes to terminate
	time.Sleep(2 * time.Second)
	
	// Check if any processes are still running
	running, err := pm.IsRunning()
	if err != nil {
		return fmt.Errorf("failed to check process status after force kill: %w", err)
	}
	
	if running {
		return fmt.Errorf("some Cursor processes are still running after force kill")
	}
	
	pm.logger.Info(utils.FormatSuccess("All Cursor processes have been force killed"))
	return nil
}
