# Cursor Manager Go - 使用指南

本文档详细介绍如何使用 Cursor Manager Go 的各种功能。

## 快速开始

### 1. 安装和启动

#### 方式一：下载预编译版本
1. 从 [Releases](https://github.com/your-repo/cursor-manager-go/releases) 下载适合您系统的版本
2. 解压到任意目录
3. 运行 Web 版本：
   ```bash
   # Windows
   cursor-manager-web.exe
   
   # macOS/Linux
   ./cursor-manager-web
   ```

#### 方式二：从源码构建
```bash
# 克隆仓库
git clone https://github.com/your-repo/cursor-manager-go.git
cd cursor-manager-go

# 构建
make build

# 运行
./bin/cursor-manager-web
```

### 2. 访问 Web 界面

启动后，打开浏览器访问：http://localhost:8080

## 功能详解

### 邮箱设置

在使用自动登录功能之前，需要先配置邮箱设置：

1. **邮箱前缀**：用于生成随机邮箱的前缀
   - 例如：设置为 `test`，会生成 `<EMAIL>` 这样的邮箱

2. **邮箱密码**：用于登录邮箱接收验证码的密码

3. **保存设置**：点击"保存设置"按钮保存配置

4. **测试连接**：点击"测试连接"验证邮箱设置是否正确

### 一键登录

一键登录是核心功能，可以自动完成整个登录流程：

#### 使用步骤：

1. **配置邮箱**：确保已正确设置邮箱前缀和密码
2. **输入目标URL**：在"目标URL"字段输入要登录的网站地址
3. **点击一键登录**：系统会自动：
   - 生成随机邮箱用于登录
   - 启动邮箱监控等待验证码
   - 打开浏览器并导航到目标网站
   - 自动填写邮箱并提交
   - 点击"邮箱验证码登录"选项
   - 等待并自动填入验证码
   - 完成登录流程

#### 注意事项：

- 确保目标网站支持邮箱验证码登录
- 登录过程中请勿关闭浏览器窗口
- 如果自动化失败，可以手动完成剩余步骤

### 快速登录

快速登录只进行浏览器自动化部分，不包含邮箱监控：

1. 输入目标URL和邮箱地址
2. 点击"快速登录"
3. 系统会打开浏览器并填写邮箱
4. 需要手动完成后续验证码输入

### 环境重置

环境重置功能用于重置 Cursor 编辑器的机器标识：

#### 功能包括：

1. **终止进程**：关闭所有 Cursor 相关进程
2. **重置机器ID**：生成新的机器标识
3. **更新数据库**：修改 Cursor 的 SQLite 数据库
4. **系统级修改**：更新系统注册表（Windows）或相关配置
5. **重启应用**：自动重新启动 Cursor

#### 使用方法：

1. 点击"重置环境"按钮
2. 确认操作（此操作不可逆）
3. 等待重置完成
4. 手动重启 Cursor（如果自动启动失败）

#### 注意事项：

- **需要管理员权限**：Windows 系统需要以管理员身份运行
- **备份重要数据**：重置前请备份重要的 Cursor 配置
- **关闭 Cursor**：重置前建议手动关闭 Cursor

### 邮箱监控

独立的邮箱监控功能，用于接收验证码：

#### 使用场景：

- 手动登录时需要验证码
- 测试邮箱接收功能
- 调试邮箱配置

#### 使用方法：

1. 确保邮箱设置正确
2. 点击"邮箱监控"开始监控
3. 在其他地方触发验证码发送
4. 系统会自动检测并显示验证码
5. 手动停止监控或等待超时

### 状态检查

查看 Cursor 应用程序的当前状态：

#### 显示信息：

- **运行状态**：Cursor 是否正在运行
- **路径有效性**：安装路径是否正确
- **数据库状态**：配置数据库是否存在
- **机器ID状态**：机器标识文件是否存在
- **详细路径**：显示各种文件和目录的完整路径

## 浏览器设置

### Chrome 路径配置

如果系统无法自动检测到 Chrome 浏览器，需要手动设置：

#### Windows 常见路径：
```
C:\Program Files\Google\Chrome\Application\chrome.exe
C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe
```

#### macOS 路径：
```
/Applications/Google Chrome.app/Contents/MacOS/Google Chrome
```

#### Linux 常见路径：
```
/usr/bin/google-chrome
/usr/bin/google-chrome-stable
/usr/bin/chromium-browser
```

### 无头模式

- **启用无头模式**：浏览器在后台运行，不显示窗口
- **禁用无头模式**：显示浏览器窗口，便于调试和观察

## 命令行界面 (CLI)

除了 Web 界面，还提供了命令行工具：

### 基本用法

```bash
# 显示帮助
cursor-manager-cli --help

# 重置环境
cursor-manager-cli reset

# 生成随机邮箱
cursor-manager-cli email generate

# 监控邮箱
cursor-manager-cli email monitor --email=<EMAIL> --password=yourpass

# 查看 Cursor 状态
cursor-manager-cli cursor status

# 重启 Cursor
cursor-manager-cli cursor restart

# 自动登录
cursor-manager-cli browser login \
  --url=https://example.com/login \
  --login-email=<EMAIL> \
  --monitoring-email=<EMAIL> \
  --password=yourpass
```

### 配置管理

```bash
# 查看当前配置
cursor-manager-cli config show

# 使用自定义配置文件
cursor-manager-cli --config=/path/to/config.yaml reset
```

## 故障排除

### 常见问题

#### 1. 邮箱连接失败
- 检查邮箱前缀和密码是否正确
- 确认网络连接正常
- 验证邮箱服务商设置

#### 2. 浏览器启动失败
- 检查 Chrome 路径设置
- 确认 Chrome 浏览器已正确安装
- 尝试禁用无头模式进行调试

#### 3. 环境重置失败
- 确认以管理员权限运行（Windows）
- 检查 Cursor 是否已完全关闭
- 查看错误日志获取详细信息

#### 4. 验证码接收超时
- 检查邮箱监控是否正常启动
- 确认目标网站已发送验证码
- 增加监控超时时间

### 日志查看

#### Web 界面日志
- 在右侧状态面板查看实时日志
- 使用"导出日志"功能保存日志文件
- 不同类型的消息用不同颜色标识

#### 命令行日志
```bash
# 启用调试模式
cursor-manager-web --debug

# 查看详细日志
cursor-manager-cli --debug reset
```

### 性能优化

#### 系统要求
- **内存**：至少 512MB 可用内存
- **磁盘**：至少 100MB 可用空间
- **网络**：稳定的互联网连接

#### 优化建议
- 关闭不必要的浏览器扩展
- 使用无头模式减少资源占用
- 定期清理日志文件
- 确保系统时间准确

## 安全注意事项

### 数据保护
- 邮箱密码存储在本地配置文件中
- 建议使用专用邮箱账户
- 定期更换邮箱密码

### 网络安全
- 仅在可信网络环境中使用
- 避免在公共网络中使用敏感功能
- 注意目标网站的安全性

### 系统安全
- 以最小权限运行（除环境重置外）
- 定期更新软件版本
- 备份重要配置和数据

## 高级用法

### 自定义配置

创建 `config.yaml` 文件：

```yaml
email:
  prefix: "your-prefix"
  password: "your-password"
  domain: "2925.com"
  timeout: 90

browser:
  chrome_path: "/path/to/chrome"
  headless: false
  timeout: 30

server:
  host: "localhost"
  port: 8080
  debug: false

cursor:
  auto_launch: true
  reset_timeout: 60
```

### 批量操作

使用脚本进行批量操作：

```bash
#!/bin/bash
# 批量重置多个环境

for i in {1..5}; do
  echo "重置环境 $i"
  cursor-manager-cli reset
  sleep 10
done
```

### API 集成

通过 REST API 集成到其他系统：

```python
import requests

# 生成邮箱
response = requests.get('http://localhost:8080/api/v1/email/generate')
email = response.json()['data']['email']

# 执行自动登录
login_data = {
    'target_url': 'https://example.com/login',
    'login_email': email,
    'monitoring_email': '<EMAIL>',
    'password': 'password123'
}
response = requests.post('http://localhost:8080/api/v1/browser/login', json=login_data)
```

## 更新和维护

### 版本更新
1. 下载最新版本
2. 备份当前配置
3. 替换可执行文件
4. 验证功能正常

### 配置迁移
- 配置文件向后兼容
- 新版本会自动迁移旧配置
- 建议定期备份配置文件

### 数据清理
```bash
# 清理日志文件
rm -f *.log

# 重置配置到默认值
cursor-manager-cli config reset
```
