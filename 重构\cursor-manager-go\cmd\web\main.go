package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/web"
	"cursor-manager-go/pkg/logger"
)

var (
	Version   = "1.0.0"
	BuildTime = "unknown"
)

func main() {
	// Parse command line flags
	var (
		configFile = flag.String("config", "", "Configuration file path")
		debug      = flag.Bool("debug", false, "Enable debug mode")
		port       = flag.Int("port", 0, "Server port (overrides config)")
		host       = flag.String("host", "", "Server host (overrides config)")
		version    = flag.Bool("version", false, "Show version information")
	)
	flag.Parse()

	// Show version information
	if *version {
		fmt.Printf("Cursor Manager Go\n")
		fmt.Printf("Version: %s\n", Version)
		fmt.Printf("Build Time: %s\n", BuildTime)
		os.Exit(0)
	}

	// Load configuration
	cfg, err := config.Load(*configFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Override config with command line flags
	if *debug {
		cfg.Debug = true
		cfg.Server.Debug = true
	}
	if *port > 0 {
		cfg.Server.Port = *port
	}
	if *host != "" {
		cfg.Server.Host = *host
	}

	// Initialize logger
	loggerConfig := &logger.Config{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	}
	if cfg.Debug {
		loggerConfig.Level = "debug"
	}

	if err := logger.Init(loggerConfig); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}

	log := logger.Get()
	log.Infof("Starting Cursor Manager Go v%s", Version)
	log.Infof("Build Time: %s", BuildTime)
	log.Infof("Debug Mode: %v", cfg.Debug)

	// Create web server
	server := web.NewServer(cfg)

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start server in a goroutine
	serverErr := make(chan error, 1)
	go func() {
		log.Infof("Starting web server on %s", server.GetAddress())
		log.Infof("Open your browser and navigate to: %s", server.GetURL())
		
		if err := server.Start(); err != nil {
			serverErr <- err
		}
	}()

	// Wait for shutdown signal or server error
	select {
	case sig := <-sigChan:
		log.Infof("Received signal: %v", sig)
		log.Info("Initiating graceful shutdown...")

		// Create shutdown context with timeout
		shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer shutdownCancel()

		// Shutdown server
		if err := server.Stop(shutdownCtx); err != nil {
			log.Errorf("Server shutdown error: %v", err)
		}

		log.Info("Shutdown completed")

	case err := <-serverErr:
		log.Errorf("Server error: %v", err)
		os.Exit(1)
	}
}
