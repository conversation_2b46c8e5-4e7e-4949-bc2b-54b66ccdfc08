import os
import sys
import platform
import random
import pywinauto
from pywinauto.application import Application

# Define emoji constants
EMOJI = {
    "INFO": "信息: ",
    "ERROR": "错误: ",
    "SUCCESS": "成功: ",
    "WARNING": "警告: ",
    "LOGIN": ""
}

def get_user_documents_path():
    """Get user documents path"""
    if platform.system() == "Windows":
        try:
            import winreg
            # 打开注册表
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders") as key:
                # 获取 "Personal" 键的值，这指向用户的文档目录
                documents_path, _ = winreg.QueryValueEx(key, "Personal")
                return documents_path
        except Exception as e:
            # fallback
            return os.path.expanduser("~\\Documents")
    else:
        return os.path.expanduser("~/Documents")
    
def get_current_chrome_url():
    """
    Gets the URL of the active tab in Google Chrome.
    Returns the URL as a string or None if not found.
    """
    try:
        app = Application(backend="uia").connect(title_re=".*- Google Chrome", class_name="Chrome_WidgetWin_1", timeout=10)
        dlg = app.top_window()
        wrapper = dlg.child_window(auto_id="view_1000", control_type="ToolBar")
        address_bar = wrapper.child_window(control_type="Edit")

        if address_bar.exists():
            url = address_bar.get_value()
            return url, dlg # Return both URL and the window handle
        else:
            print("Error: Could not find the address bar.")
            return None, None

    except pywinauto.findwindows.ElementNotFoundError:
        print("Error: Google Chrome window not found.")
        return None, None
    except Exception as e:
        print(f"An unknown error occurred: {e}")
        return None, None

def get_default_driver_path(browser_type='chrome'):
    """Get default driver path for Chrome."""
    return get_default_chrome_driver_path()

def get_default_chrome_driver_path():
    """Get default Chrome driver path"""
    if sys.platform == "win32":
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), "drivers", "chromedriver.exe")
    elif sys.platform == "darwin":
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), "drivers", "chromedriver")
    else:
        return "/usr/local/bin/chromedriver"

def get_default_browser_path(browser_type='chrome'):
    """Get default Chrome browser executable path"""
    # Platform-specific logic
    if sys.platform == "win32":
        exe_names = ["chrome.exe"]
        
        # 1. Try to find via Windows Registry App Paths
        try:
            import winreg
            for name in exe_names:
                key_path = rf"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\{name}"
                for root in [winreg.HKEY_LOCAL_MACHINE, winreg.HKEY_CURRENT_USER]:
                    try:
                        with winreg.OpenKey(root, key_path) as key:
                            path, _ = winreg.QueryValueEx(key, "")
                            if os.path.exists(path):
                                return path
                    except FileNotFoundError:
                        continue
        except (ImportError, OSError):
            pass # winreg might not be available or other OS error

        # 2. Try to find in PATH environment variable
        try:
            import shutil
            for name in exe_names:
                path_from_env = shutil.which(name)
                if path_from_env:
                    return path_from_env
        except ImportError:
            pass

        # 3. Fallback to hardcoded default paths
        return r"C:\Program Files\Google\Chrome\Application\chrome.exe"

    elif sys.platform == "darwin":
        # macOS paths
        return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            
    else:  # Linux
        # For Linux, rely on shutil.which to find in PATH
        try:
            import shutil
            # Create a list of possible executable names
            names_to_try = ["chrome", "google-chrome", "chromium", "chromium-browser"]
            
            for name in names_to_try:
                path = shutil.which(name)
                if path:
                    return path
        except ImportError:
            pass # shutil might not be available
        
        # Fallback to common hardcoded paths for Linux
        return "/usr/bin/google-chrome"

def get_linux_cursor_path():
    """Get Linux Cursor path"""
    possible_paths = [
        "/opt/Cursor/resources/app",
        "/usr/share/cursor/resources/app",
        "/opt/cursor-bin/resources/app",
        "/usr/lib/cursor/resources/app",
        os.path.expanduser("~/.local/share/cursor/resources/app")
    ]
    
    # return the first path that exists
    return next((path for path in possible_paths if os.path.exists(path)), possible_paths[0])

def get_random_wait_time(config, timing_key):
    """Get random wait time based on configuration timing settings
    
    Args:
        config (dict): Configuration dictionary containing timing settings
        timing_key (str): Key to look up in the timing settings
        
    Returns:
        float: Random wait time in seconds
    """
    try:
        # Get timing value from config
        timing = config.get('Timing', {}).get(timing_key)
        if not timing:
            # Default to 0.5-1.5 seconds if timing not found
            return random.uniform(0.5, 1.5)
            
        # Check if timing is a range (e.g., "0.5-1.5" or "0.5,1.5")
        if isinstance(timing, str):
            if '-' in timing:
                min_time, max_time = map(float, timing.split('-'))
            elif ',' in timing:
                min_time, max_time = map(float, timing.split(','))
            else:
                # Single value, use it as both min and max
                min_time = max_time = float(timing)
        else:
            # If timing is a number, use it as both min and max
            min_time = max_time = float(timing)
            
        return random.uniform(min_time, max_time)
        
    except (ValueError, TypeError, AttributeError):
        # Return default value if any error occurs
        return random.uniform(0.5, 1.5) 