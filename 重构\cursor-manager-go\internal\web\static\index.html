<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor Manager Go</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <span class="emoji">🚀</span>
                    Cursor Manager Go
                </h1>
                <div class="status-indicator" id="connectionStatus">
                    <span class="status-dot"></span>
                    <span class="status-text">连接中...</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Auto Login Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="emoji">🎯</span>
                            一键登录工具
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">当前邮箱</label>
                            <div class="input-group">
                                <input type="text" id="currentEmail" class="form-input" readonly>
                                <button class="btn btn-secondary" onclick="copyEmail()">
                                    <span class="emoji">📋</span> 复制
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标URL</label>
                            <input type="text" id="targetUrl" class="form-input" placeholder="输入登录页面URL">
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary" onclick="autoLogin()">
                            <span class="emoji">🚀</span> 一键登录
                        </button>
                        <button class="btn btn-secondary" onclick="quickLogin()">
                            <span class="emoji">⚡</span> 快速登录
                        </button>
                    </div>
                </div>

                <!-- Email Settings Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="emoji">📧</span>
                            邮箱设置
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">邮箱前缀</label>
                            <input type="text" id="emailPrefix" class="form-input" placeholder="输入邮箱前缀">
                        </div>
                        <div class="form-group">
                            <label class="form-label">邮箱密码</label>
                            <input type="password" id="emailPassword" class="form-input" placeholder="输入邮箱密码">
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-success" onclick="saveEmailConfig()">
                            <span class="emoji">💾</span> 保存设置
                        </button>
                        <button class="btn btn-secondary" onclick="testEmailConnection()">
                            <span class="emoji">🔍</span> 测试连接
                        </button>
                    </div>
                </div>

                <!-- Browser Settings Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="emoji">🌐</span>
                            浏览器设置
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">Chrome路径</label>
                            <input type="text" id="browserPath" class="form-input" placeholder="Chrome浏览器路径">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="headlessMode">
                                <span class="checkmark"></span>
                                无头模式
                            </label>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-success" onclick="saveBrowserConfig()">
                            <span class="emoji">💾</span> 保存设置
                        </button>
                    </div>
                </div>

                <!-- System Tools Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="emoji">🔧</span>
                            系统工具
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="tool-buttons">
                            <button class="btn btn-warning" onclick="resetEnvironment()">
                                <span class="emoji">🔄</span> 重置环境
                            </button>
                            <button class="btn btn-info" onclick="restartCursor()">
                                <span class="emoji">🔃</span> 重启Cursor
                            </button>
                            <button class="btn btn-primary" onclick="startEmailMonitoring()">
                                <span class="emoji">📧</span> 邮箱监控
                            </button>
                            <button class="btn btn-secondary" onclick="getCursorStatus()">
                                <span class="emoji">📊</span> 状态检查
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Status -->
            <div class="right-panel">
                <div class="status-panel">
                    <div class="status-header">
                        <h3 class="status-title">
                            <span class="emoji">💻</span>
                            运行状态
                        </h3>
                        <div class="status-controls">
                            <button class="btn-icon" onclick="clearStatus()" title="清空日志">
                                <span class="emoji">🗑️</span>
                            </button>
                            <button class="btn-icon" onclick="exportLogs()" title="导出日志">
                                <span class="emoji">📤</span>
                            </button>
                        </div>
                    </div>
                    <div class="status-content" id="statusContent">
                        <div class="status-message info">
                            <span class="timestamp">00:00:00</span>
                            <span class="message">应用程序已启动，准备就绪</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <span class="footer-text">Cursor Manager Go v1.0.0</span>
                <div class="footer-links">
                    <button class="link-button" onclick="showSystemInfo()">系统信息</button>
                    <button class="link-button" onclick="showHelp()">帮助</button>
                    <button class="link-button" onclick="showAbout()">关于</button>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">处理中...</div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Modal -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">标题</h3>
                <button class="modal-close" onclick="closeModal()">
                    <span class="emoji">✖️</span>
                </button>
            </div>
            <div class="modal-content" id="modalContent">
                内容
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal()">关闭</button>
            </div>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>
