# Cursor Manager Go - Makefile

# Variables
BINARY_NAME=cursor-manager
VERSION=1.0.0
BUILD_DIR=bin
GO_VERSION=1.21

# Build flags
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(shell date -u +%Y-%m-%dT%H:%M:%SZ)"

# Default target
.PHONY: all
all: clean build

# Clean build directory
.PHONY: clean
clean:
	@echo "Cleaning build directory..."
	@rm -rf $(BUILD_DIR)
	@mkdir -p $(BUILD_DIR)

# Download dependencies
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	@go mod download
	@go mod tidy

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	@go test -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html

# Build web version
.PHONY: build-web
build-web:
	@echo "Building web version..."
	@go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-web ./cmd/web

# Build CLI version
.PHONY: build-cli
build-cli:
	@echo "Building CLI version..."
	@go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-cli ./cmd/cli

# Build GUI version (future)
.PHONY: build-gui
build-gui:
	@echo "Building GUI version..."
	@go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-gui ./cmd/gui

# Build all versions
.PHONY: build-all
build-all: build-web build-cli build-gui

# Build for current platform
.PHONY: build
build: build-web build-cli

# Cross-compile for all platforms
.PHONY: build-cross
build-cross: clean
	@echo "Cross-compiling for all platforms..."
	# Windows
	@GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/windows/$(BINARY_NAME)-web.exe ./cmd/web
	@GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/windows/$(BINARY_NAME)-cli.exe ./cmd/cli
	# macOS
	@GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/darwin/$(BINARY_NAME)-web ./cmd/web
	@GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/darwin/$(BINARY_NAME)-cli ./cmd/cli
	@GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o $(BUILD_DIR)/darwin-arm64/$(BINARY_NAME)-web ./cmd/web
	@GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o $(BUILD_DIR)/darwin-arm64/$(BINARY_NAME)-cli ./cmd/cli
	# Linux
	@GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/linux/$(BINARY_NAME)-web ./cmd/web
	@GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/linux/$(BINARY_NAME)-cli ./cmd/cli

# Run development server
.PHONY: dev
dev:
	@echo "Starting development server..."
	@go run ./cmd/web --debug

# Run CLI in development
.PHONY: dev-cli
dev-cli:
	@echo "Running CLI in development mode..."
	@go run ./cmd/cli

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# Lint code
.PHONY: lint
lint:
	@echo "Linting code..."
	@golangci-lint run

# Install tools
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Generate documentation
.PHONY: docs
docs:
	@echo "Generating documentation..."
	@go doc -all ./... > docs/API.md

# Install binary to system
.PHONY: install
install: build
	@echo "Installing binary to system..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME)-web /usr/local/bin/
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME)-cli /usr/local/bin/

# Uninstall binary from system
.PHONY: uninstall
uninstall:
	@echo "Uninstalling binary from system..."
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)-web
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)-cli

# Show help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean and build all versions"
	@echo "  clean        - Clean build directory"
	@echo "  deps         - Download dependencies"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  build        - Build web and CLI versions"
	@echo "  build-web    - Build web version only"
	@echo "  build-cli    - Build CLI version only"
	@echo "  build-gui    - Build GUI version only"
	@echo "  build-all    - Build all versions"
	@echo "  build-cross  - Cross-compile for all platforms"
	@echo "  dev          - Run development server"
	@echo "  dev-cli      - Run CLI in development"
	@echo "  fmt          - Format code"
	@echo "  lint         - Lint code"
	@echo "  install-tools- Install development tools"
	@echo "  docs         - Generate documentation"
	@echo "  install      - Install binary to system"
	@echo "  uninstall    - Uninstall binary from system"
	@echo "  help         - Show this help"
