package cursor

import (
	"os"
	"path/filepath"
	"runtime"

	"cursor-manager-go/internal/utils"
)

// Paths represents Cursor application paths
type Paths struct {
	DataPath     string // Cursor data directory
	DBPath       string // SQLite database path
	MachineIDPath string // Machine ID file path
	AppPath      string // Cursor executable path
}

// GetCursorPaths returns the Cursor application paths for the current OS
func GetCursorPaths() (*Paths, error) {
	switch runtime.GOOS {
	case "windows":
		return getWindowsPaths()
	case "darwin":
		return getDarwinPaths()
	case "linux":
		return getLinuxPaths()
	default:
		return nil, utils.FormatError("unsupported operating system: " + runtime.GOOS)
	}
}

// getWindowsPaths returns Cursor paths for Windows
func getWindowsPaths() (*Paths, error) {
	appData := os.Getenv("APPDATA")
	if appData == "" {
		return nil, utils.FormatError("APPDATA environment variable not found")
	}

	dataPath := filepath.Join(appData, "Cursor")
	dbPath := filepath.Join(dataPath, "User", "globalStorage", "state.vscdb")
	machineIDPath := filepath.Join(dataPath, "machineId")

	// Find Cursor executable
	var appPath string
	possiblePaths := []string{
		filepath.Join(os.Getenv("LOCALAPPDATA"), "Programs", "Cursor", "Cursor.exe"),
		filepath.Join(os.Getenv("ProgramFiles"), "cursor", "Cursor.exe"),
		filepath.Join(os.Getenv("ProgramFiles(x86)"), "cursor", "Cursor.exe"),
	}

	for _, path := range possiblePaths {
		if utils.FileExists(path) {
			appPath = path
			break
		}
	}

	if appPath == "" {
		// Use the most common path as fallback
		appPath = possiblePaths[0]
	}

	return &Paths{
		DataPath:      dataPath,
		DBPath:        dbPath,
		MachineIDPath: machineIDPath,
		AppPath:       appPath,
	}, nil
}

// getDarwinPaths returns Cursor paths for macOS
func getDarwinPaths() (*Paths, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, utils.FormatError("failed to get user home directory: " + err.Error())
	}

	dataPath := filepath.Join(homeDir, "Library", "Application Support", "Cursor")
	dbPath := filepath.Join(dataPath, "User", "globalStorage", "state.vscdb")
	machineIDPath := filepath.Join(dataPath, "machineId")
	appPath := "/Applications/Cursor.app"

	return &Paths{
		DataPath:      dataPath,
		DBPath:        dbPath,
		MachineIDPath: machineIDPath,
		AppPath:       appPath,
	}, nil
}

// getLinuxPaths returns Cursor paths for Linux
func getLinuxPaths() (*Paths, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, utils.FormatError("failed to get user home directory: " + err.Error())
	}

	dataPath := filepath.Join(homeDir, ".config", "cursor")
	dbPath := filepath.Join(dataPath, "User", "globalStorage", "state.vscdb")
	machineIDPath := filepath.Join(dataPath, "machineid") // Note: lowercase on Linux

	// Find Cursor executable
	var appPath string
	possiblePaths := []string{
		"/usr/bin/cursor",
		"/usr/local/bin/cursor",
		filepath.Join(homeDir, "Applications", "Cursor.AppImage"),
		filepath.Join(homeDir, ".local", "bin", "cursor"),
	}

	for _, path := range possiblePaths {
		if utils.FileExists(path) {
			appPath = path
			break
		}
	}

	if appPath == "" {
		// Use the most common path as fallback
		appPath = possiblePaths[0]
	}

	return &Paths{
		DataPath:      dataPath,
		DBPath:        dbPath,
		MachineIDPath: machineIDPath,
		AppPath:       appPath,
	}, nil
}

// Validate checks if the paths exist and are accessible
func (p *Paths) Validate() error {
	// Check if data directory exists
	if !utils.DirExists(p.DataPath) {
		return utils.FormatError("Cursor data directory not found: " + p.DataPath)
	}

	// Check if database file exists (it's okay if it doesn't exist yet)
	if utils.FileExists(p.DBPath) {
		// Check if it's readable
		if _, err := os.Open(p.DBPath); err != nil {
			return utils.FormatError("cannot access Cursor database: " + err.Error())
		}
	}

	// Check if app executable exists
	if !utils.FileExists(p.AppPath) {
		return utils.FormatError("Cursor executable not found: " + p.AppPath)
	}

	return nil
}

// EnsureDirectories creates necessary directories if they don't exist
func (p *Paths) EnsureDirectories() error {
	directories := []string{
		p.DataPath,
		filepath.Dir(p.DBPath),
		filepath.Dir(p.MachineIDPath),
	}

	for _, dir := range directories {
		if err := utils.CreateDir(dir); err != nil {
			return utils.FormatError("failed to create directory " + dir + ": " + err.Error())
		}
	}

	return nil
}

// GetBackupPath returns a backup path for the given file
func (p *Paths) GetBackupPath(originalPath string) string {
	dir := filepath.Dir(originalPath)
	filename := filepath.Base(originalPath)
	ext := filepath.Ext(filename)
	nameWithoutExt := filename[:len(filename)-len(ext)]
	
	timestamp := utils.GetTimestamp()
	backupFilename := nameWithoutExt + "_backup_" + timestamp + ext
	
	return filepath.Join(dir, backupFilename)
}

// GetTempPath returns a temporary path for the given file
func (p *Paths) GetTempPath(originalPath string) string {
	dir := filepath.Dir(originalPath)
	filename := filepath.Base(originalPath)
	ext := filepath.Ext(filename)
	nameWithoutExt := filename[:len(filename)-len(ext)]
	
	tempFilename := nameWithoutExt + "_temp" + ext
	
	return filepath.Join(dir, tempFilename)
}
