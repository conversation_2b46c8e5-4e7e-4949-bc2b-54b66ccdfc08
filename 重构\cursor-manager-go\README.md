# Cursor Manager Go

A modern Cursor editor management tool written in Go, providing automated login, machine ID reset, and email verification code monitoring.

## Features

- 🚀 **One-Click Login**: Automated login flow with email verification
- 🔄 **Environment Reset**: Reset Cursor machine ID and environment
- 📧 **Email Monitoring**: Automatic verification code detection
- 🌐 **Web Interface**: Modern web-based user interface
- 🖥️ **Cross-Platform**: Support for Windows, macOS, and Linux
- ⚡ **High Performance**: Built with Go for speed and efficiency

## Quick Start

### Prerequisites

- Go 1.21 or later
- Chrome/Chromium browser
- Email account for verification

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd cursor-manager-go

# Build the application
make build

# Or build specific version
make build-web    # Web interface version
make build-gui    # GUI version (future)
make build-cli    # CLI version
```

### Usage

#### Web Interface (Recommended)

```bash
# Start web server
./bin/cursor-manager-web

# Open browser and navigate to http://localhost:8080
```

#### CLI Version

```bash
# Reset environment
./bin/cursor-manager-cli reset

# Generate random email
./bin/cursor-manager-cli email generate

# Start email monitoring
./bin/cursor-manager-cli email monitor --email=<EMAIL> --password=yourpass
```

## Configuration

The application uses a configuration file `config.yaml`:

```yaml
email:
  prefix: "your-prefix"
  password: "your-password"
  domain: "2925.com"

browser:
  chrome_path: "/path/to/chrome"
  headless: false
  timeout: 30

server:
  host: "localhost"
  port: 8080
  debug: false

cursor:
  auto_launch: true
  reset_timeout: 60
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   CLI Interface │    │   GUI Interface │
│   (HTML/JS/CSS) │    │   (Cobra CLI)   │    │   (Future)      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────────┐
                    │        Core Services        │
                    │  ┌─────────────────────────┐ │
                    │  │   Config Management     │ │
                    │  │   Cursor App Manager    │ │
                    │  │   Email Monitor         │ │
                    │  │   Browser Automation    │ │
                    │  │   Machine ID Reset      │ │
                    │  └─────────────────────────┘ │
                    └─────────────────────────────┘
```

## Development

### Project Structure

```
cursor-manager-go/
├── cmd/                    # Application entry points
├── internal/               # Private application code
│   ├── config/            # Configuration management
│   ├── cursor/            # Cursor application management
│   ├── email/             # Email monitoring and generation
│   ├── browser/           # Browser automation
│   ├── utils/             # Utility functions
│   └── web/               # Web server and handlers
├── pkg/                   # Public libraries
└── scripts/               # Build and deployment scripts
```

### Building from Source

```bash
# Install dependencies
go mod download

# Run tests
make test

# Build all versions
make build-all

# Run development server
make dev
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Original Python version for inspiration
- Go community for excellent libraries
- Contributors and testers
