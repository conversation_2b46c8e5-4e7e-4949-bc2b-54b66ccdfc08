@echo off
REM Cursor Manager Go - Windows Build Script

setlocal enabledelayedexpansion

REM Configuration
set APP_NAME=cursor-manager
set VERSION=1.0.0
set BUILD_DIR=bin

REM Get current timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "BUILD_TIME=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%T%dt:~8,2%:%dt:~10,2%:%dt:~12,2%Z"

echo Cursor Manager Go - Windows Build Script
echo ========================================
echo Version: %VERSION%
echo Build Time: %BUILD_TIME%
echo.

REM Check if Go is installed
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Go is not installed or not in PATH
    exit /b 1
)

for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
echo [INFO] Using Go version: %GO_VERSION%

REM Parse command line argument
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=build

if "%COMMAND%"=="clean" goto clean
if "%COMMAND%"=="deps" goto deps
if "%COMMAND%"=="test" goto test
if "%COMMAND%"=="build" goto build
if "%COMMAND%"=="build-all" goto build_all
if "%COMMAND%"=="dev" goto dev
if "%COMMAND%"=="install" goto install
if "%COMMAND%"=="help" goto help
if "%COMMAND%"=="--help" goto help
if "%COMMAND%"=="-h" goto help

echo [ERROR] Unknown command: %COMMAND%
goto help

:clean
echo [INFO] Cleaning build directory...
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
mkdir "%BUILD_DIR%"
echo [SUCCESS] Build directory cleaned
goto end

:deps
echo [INFO] Downloading dependencies...
go mod download
go mod tidy
echo [SUCCESS] Dependencies downloaded
goto end

:test
echo [INFO] Running tests...
go test -v ./...
if %errorlevel% neq 0 (
    echo [ERROR] Tests failed
    exit /b 1
)
echo [SUCCESS] Tests passed
goto end

:build
echo [INFO] Building for Windows...
call :clean
call :deps

set LDFLAGS=-X main.Version=%VERSION% -X main.BuildTime=%BUILD_TIME%

echo [INFO] Building web version...
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\%APP_NAME%-web.exe" ./cmd/web
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build web version
    exit /b 1
)

echo [INFO] Building CLI version...
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\%APP_NAME%-cli.exe" ./cmd/cli
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build CLI version
    exit /b 1
)

echo [INFO] Building GUI version...
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\%APP_NAME%-gui.exe" ./cmd/gui
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build GUI version
    exit /b 1
)

echo [SUCCESS] Build completed for Windows
echo.
echo Built files:
dir /b "%BUILD_DIR%\*.exe"
goto end

:build_all
echo [INFO] Building for all platforms...
call :clean
call :deps

set LDFLAGS=-X main.Version=%VERSION% -X main.BuildTime=%BUILD_TIME%

REM Windows builds
echo [INFO] Building for Windows amd64...
set GOOS=windows
set GOARCH=amd64
mkdir "%BUILD_DIR%\windows-amd64" 2>nul
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\windows-amd64\%APP_NAME%-web.exe" ./cmd/web
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\windows-amd64\%APP_NAME%-cli.exe" ./cmd/cli
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\windows-amd64\%APP_NAME%-gui.exe" ./cmd/gui

echo [INFO] Building for Windows 386...
set GOOS=windows
set GOARCH=386
mkdir "%BUILD_DIR%\windows-386" 2>nul
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\windows-386\%APP_NAME%-web.exe" ./cmd/web
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\windows-386\%APP_NAME%-cli.exe" ./cmd/cli
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\windows-386\%APP_NAME%-gui.exe" ./cmd/gui

REM Linux builds
echo [INFO] Building for Linux amd64...
set GOOS=linux
set GOARCH=amd64
mkdir "%BUILD_DIR%\linux-amd64" 2>nul
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\linux-amd64\%APP_NAME%-web" ./cmd/web
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\linux-amd64\%APP_NAME%-cli" ./cmd/cli
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\linux-amd64\%APP_NAME%-gui" ./cmd/gui

REM macOS builds
echo [INFO] Building for macOS amd64...
set GOOS=darwin
set GOARCH=amd64
mkdir "%BUILD_DIR%\darwin-amd64" 2>nul
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\darwin-amd64\%APP_NAME%-web" ./cmd/web
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\darwin-amd64\%APP_NAME%-cli" ./cmd/cli
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\darwin-amd64\%APP_NAME%-gui" ./cmd/gui

echo [INFO] Building for macOS arm64...
set GOOS=darwin
set GOARCH=arm64
mkdir "%BUILD_DIR%\darwin-arm64" 2>nul
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\darwin-arm64\%APP_NAME%-web" ./cmd/web
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\darwin-arm64\%APP_NAME%-cli" ./cmd/cli
go build -ldflags "%LDFLAGS%" -o "%BUILD_DIR%\darwin-arm64\%APP_NAME%-gui" ./cmd/gui

echo [SUCCESS] Cross-compilation completed
goto end

:dev
echo [INFO] Starting development server...
go run ./cmd/web --debug --port 8080
goto end

:install
echo [INFO] Installing to system...
if not exist "%BUILD_DIR%\%APP_NAME%-web.exe" (
    echo [ERROR] Binaries not found. Please run 'build' first.
    exit /b 1
)

REM Try to copy to a directory in PATH
copy "%BUILD_DIR%\%APP_NAME%-*.exe" "%USERPROFILE%\bin\" 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Installed to %USERPROFILE%\bin\
    echo [INFO] Make sure %USERPROFILE%\bin is in your PATH
) else (
    echo [WARNING] Could not install to %USERPROFILE%\bin\
    echo [INFO] Please manually copy the executables to a directory in your PATH
    echo [INFO] Built files are in: %BUILD_DIR%\
)
goto end

:help
echo Usage: build.bat [command]
echo.
echo Available commands:
echo   clean     - Clean build directory
echo   deps      - Download dependencies
echo   test      - Run tests
echo   build     - Build for Windows (default)
echo   build-all - Build for all platforms
echo   dev       - Start development server
echo   install   - Install to system
echo   help      - Show this help
echo.
echo Examples:
echo   build.bat build
echo   build.bat dev
echo   build.bat build-all
goto end

:end
echo.
echo Build script completed.
