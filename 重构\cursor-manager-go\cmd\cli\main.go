package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"cursor-manager-go/internal/browser"
	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/cursor"
	"cursor-manager-go/internal/email"
	"cursor-manager-go/pkg/logger"

	"github.com/spf13/cobra"
)

var (
	Version   = "1.0.0"
	BuildTime = "unknown"
	cfg       *config.Config
)

func main() {
	var rootCmd = &cobra.Command{
		Use:   "cursor-manager-cli",
		Short: "Cursor Manager CLI - Command line interface for Cursor management",
		Long: `Cursor Manager CLI provides command line access to Cursor management features
including environment reset, email monitoring, and automated login.`,
		Version: Version,
	}

	// Global flags
	var configFile string
	var debug bool
	rootCmd.PersistentFlags().StringVar(&configFile, "config", "", "config file path")
	rootCmd.PersistentFlags().BoolVar(&debug, "debug", false, "enable debug mode")

	// Initialize configuration before running commands
	rootCmd.PersistentPreRunE = func(cmd *cobra.Command, args []string) error {
		var err error
		cfg, err = config.Load(configFile)
		if err != nil {
			return fmt.Errorf("failed to load config: %w", err)
		}

		if debug {
			cfg.Debug = true
		}

		// Initialize logger
		loggerConfig := &logger.Config{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		}
		if cfg.Debug {
			loggerConfig.Level = "debug"
		}

		return logger.Init(loggerConfig)
	}

	// Add subcommands
	rootCmd.AddCommand(createResetCommand())
	rootCmd.AddCommand(createEmailCommand())
	rootCmd.AddCommand(createBrowserCommand())
	rootCmd.AddCommand(createCursorCommand())
	rootCmd.AddCommand(createConfigCommand())

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func createResetCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "reset",
		Short: "Reset Cursor environment",
		Long:  "Terminates Cursor processes and resets machine ID",
		RunE: func(cmd *cobra.Command, args []string) error {
			log := logger.Get()
			log.Info("Starting environment reset...")

			manager, err := cursor.NewManager()
			if err != nil {
				return fmt.Errorf("failed to create cursor manager: %w", err)
			}

			if err := manager.FullReset(); err != nil {
				return fmt.Errorf("environment reset failed: %w", err)
			}

			log.Info("Environment reset completed successfully")
			return nil
		},
	}

	return cmd
}

func createEmailCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "email",
		Short: "Email management commands",
		Long:  "Commands for email generation, monitoring, and testing",
	}

	// Generate subcommand
	generateCmd := &cobra.Command{
		Use:   "generate",
		Short: "Generate a random email",
		RunE: func(cmd *cobra.Command, args []string) error {
			generator := email.NewGenerator(cfg)
			email, err := generator.GenerateRandom()
			if err != nil {
				return fmt.Errorf("failed to generate email: %w", err)
			}

			fmt.Println(email)
			return nil
		},
	}

	// Monitor subcommand
	monitorCmd := &cobra.Command{
		Use:   "monitor",
		Short: "Monitor email for verification codes",
		RunE: func(cmd *cobra.Command, args []string) error {
			emailAddr, _ := cmd.Flags().GetString("email")
			password, _ := cmd.Flags().GetString("password")
			timeout, _ := cmd.Flags().GetDuration("timeout")

			if emailAddr == "" || password == "" {
				return fmt.Errorf("email and password are required")
			}

			log := logger.Get()
			log.Infof("Starting email monitoring for: %s", emailAddr)

			monitor := email.NewMonitor(cfg)
			ctx, cancel := context.WithTimeout(context.Background(), timeout)
			defer cancel()

			monitorConfig := email.DefaultMonitorConfig(emailAddr, password)
			monitorConfig.Timeout = timeout

			if err := monitor.Start(ctx, monitorConfig); err != nil {
				return fmt.Errorf("failed to start email monitoring: %w", err)
			}

			// Wait for verification code
			code, err := monitor.WaitForCode(timeout)
			if err != nil {
				return fmt.Errorf("failed to get verification code: %w", err)
			}

			fmt.Printf("Verification code: %s\n", code)
			return nil
		},
	}

	monitorCmd.Flags().String("email", "", "Email address to monitor")
	monitorCmd.Flags().String("password", "", "Email password")
	monitorCmd.Flags().Duration("timeout", 90*time.Second, "Monitoring timeout")
	monitorCmd.MarkFlagRequired("email")
	monitorCmd.MarkFlagRequired("password")

	cmd.AddCommand(generateCmd)
	cmd.AddCommand(monitorCmd)

	return cmd
}

func createBrowserCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "browser",
		Short: "Browser automation commands",
		Long:  "Commands for automated login and browser control",
	}

	// Login subcommand
	loginCmd := &cobra.Command{
		Use:   "login",
		Short: "Perform automated login",
		RunE: func(cmd *cobra.Command, args []string) error {
			targetURL, _ := cmd.Flags().GetString("url")
			loginEmail, _ := cmd.Flags().GetString("login-email")
			monitoringEmail, _ := cmd.Flags().GetString("monitoring-email")
			password, _ := cmd.Flags().GetString("password")

			if targetURL == "" || loginEmail == "" || monitoringEmail == "" || password == "" {
				return fmt.Errorf("all flags are required: url, login-email, monitoring-email, password")
			}

			log := logger.Get()
			log.Info("Starting automated login...")

			loginManager := browser.NewLoginManager(cfg)
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
			defer cancel()

			request := &browser.LoginRequest{
				TargetURL:       targetURL,
				LoginEmail:      loginEmail,
				MonitoringEmail: monitoringEmail,
				Password:        password,
			}

			result, err := loginManager.PerformAutoLogin(ctx, request)
			if err != nil {
				return fmt.Errorf("auto-login failed: %w", err)
			}

			if result.Success {
				log.Info("Auto-login completed successfully")
				if result.FinalURL != "" {
					log.Infof("Final URL: %s", result.FinalURL)
				}
			} else {
				return fmt.Errorf("auto-login failed: %s", result.Error)
			}

			return nil
		},
	}

	loginCmd.Flags().String("url", "", "Target URL for login")
	loginCmd.Flags().String("login-email", "", "Email to use for login")
	loginCmd.Flags().String("monitoring-email", "", "Email to monitor for verification codes")
	loginCmd.Flags().String("password", "", "Email password")
	loginCmd.MarkFlagRequired("url")
	loginCmd.MarkFlagRequired("login-email")
	loginCmd.MarkFlagRequired("monitoring-email")
	loginCmd.MarkFlagRequired("password")

	cmd.AddCommand(loginCmd)

	return cmd
}

func createCursorCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "cursor",
		Short: "Cursor application management",
		Long:  "Commands for managing Cursor application processes and status",
	}

	// Status subcommand
	statusCmd := &cobra.Command{
		Use:   "status",
		Short: "Get Cursor status",
		RunE: func(cmd *cobra.Command, args []string) error {
			manager, err := cursor.NewManager()
			if err != nil {
				return fmt.Errorf("failed to create cursor manager: %w", err)
			}

			status, err := manager.GetStatus()
			if err != nil {
				return fmt.Errorf("failed to get status: %w", err)
			}

			fmt.Printf("Cursor Status:\n")
			fmt.Printf("  Running: %v\n", status.IsRunning)
			fmt.Printf("  Paths Valid: %v\n", status.PathsValid)
			fmt.Printf("  Database Exists: %v\n", status.DatabaseExists)
			fmt.Printf("  Machine ID Exists: %v\n", status.MachineIDExists)
			fmt.Printf("  Data Path: %s\n", status.Paths.DataPath)
			fmt.Printf("  App Path: %s\n", status.Paths.AppPath)

			return nil
		},
	}

	// Restart subcommand
	restartCmd := &cobra.Command{
		Use:   "restart",
		Short: "Restart Cursor application",
		RunE: func(cmd *cobra.Command, args []string) error {
			manager, err := cursor.NewManager()
			if err != nil {
				return fmt.Errorf("failed to create cursor manager: %w", err)
			}

			log := logger.Get()
			log.Info("Restarting Cursor...")

			if err := manager.RestartCursor(); err != nil {
				return fmt.Errorf("failed to restart Cursor: %w", err)
			}

			log.Info("Cursor restart completed")
			return nil
		},
	}

	cmd.AddCommand(statusCmd)
	cmd.AddCommand(restartCmd)

	return cmd
}

func createConfigCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "config",
		Short: "Configuration management",
		Long:  "Commands for viewing and managing configuration",
	}

	// Show subcommand
	showCmd := &cobra.Command{
		Use:   "show",
		Short: "Show current configuration",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Printf("Configuration:\n")
			fmt.Printf("  Email Prefix: %s\n", cfg.Email.Prefix)
			fmt.Printf("  Email Domain: %s\n", cfg.Email.Domain)
			fmt.Printf("  Browser Path: %s\n", cfg.Browser.ChromePath)
			fmt.Printf("  Browser Headless: %v\n", cfg.Browser.Headless)
			fmt.Printf("  Server Host: %s\n", cfg.Server.Host)
			fmt.Printf("  Server Port: %d\n", cfg.Server.Port)
			fmt.Printf("  Debug: %v\n", cfg.Debug)

			return nil
		},
	}

	cmd.AddCommand(showCmd)

	return cmd
}
