"""
本地界面管理器
替代Flask服务器，直接通过pywebview的JS API提供功能
"""

import os
import sys
import threading
import time
import traceback

# 设置控制台编码为UTF-8，避免emoji显示问题
if sys.platform == 'win32':
    try:
        # 尝试设置控制台为UTF-8编码
        os.system('chcp 65001 >nul 2>&1')
        # 重新配置stdout编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
    except:
        # 如果设置失败，使用安全的打印函数
        pass

def safe_print(text):
    """安全的打印函数，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果遇到编码错误，移除emoji字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

# 添加父目录到路径，以便导入原有模块
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入原有模块
try:
    from 配置管理 import get_config, save_config
    from 应用管理 import run_full_reset_flow, launch_email_client_process
    from 网页操作 import generate_random_email, run_auto_login_flow
    from 工具模块 import EMOJI
    safe_print("[DEBUG] 所有模块导入成功")
except ImportError as e:
    safe_print(f"[ERROR] 模块导入失败: {e}")
    # 提供备用函数
    def get_config():
        return None
    def save_config(config):
        return False
    def run_full_reset_flow(callback):
        callback("模块不可用")
    def launch_email_client_process(email, password, callback):
        callback("模块不可用")
    def generate_random_email(callback=None):
        return "<EMAIL>"
    def run_auto_login_flow(monitoring_email, login_email, password, callback):
        callback("模块不可用")
    EMOJI = {'SUCCESS': '✅', 'ERROR': '❌', 'WARNING': '⚠️', 'INFO': 'ℹ️'}

class LocalInterfaceAPI:
    """本地界面API类，提供所有前端需要的功能"""
    
    def __init__(self):
        self.status_messages = []
        self.config = get_config()
        safe_print("[DEBUG] 本地界面API初始化完成")
    
    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = time.strftime('%H:%M:%S')
        self.status_messages.append({
            'timestamp': timestamp,
            'message': message
        })
        safe_print(f"[STATUS] {timestamp} - {message}")
        
        # 处理特殊的验证码消息
        if isinstance(message, str) and message.startswith("COPY_AND_SHOW:"):
            try:
                code = message.split(":", 1)[1]
                self.status_messages.append({
                    'timestamp': timestamp,
                    'message': f"成功获取到验证码: {code} (已复制)",
                    'type': 'verification_code',
                    'code': code
                })
            except IndexError:
                pass
    
    def get_config(self):
        """获取配置信息"""
        try:
            config = get_config()
            if config:
                return {
                    'success': True,
                    'data': {
                        'email_prefix': config.get('Email', 'prefix', fallback=''),
                        'email_password': config.get('Email', 'password', fallback=''),
                        'browser_path': config.get('Browser', 'chrome_path', fallback='')
                    }
                }
            else:
                return {'success': False, 'error': '无法加载配置'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def save_config(self, data):
        """保存配置信息"""
        try:
            config = get_config()
            if not config:
                return {'success': False, 'error': '无法获取配置对象'}
            
            if 'email_prefix' in data:
                config.set('Email', 'prefix', data['email_prefix'])
            if 'email_password' in data:
                config.set('Email', 'password', data['email_password'])
            if 'browser_path' in data:
                config.set('Browser', 'chrome_path', data['browser_path'])
            
            if save_config(config):
                self.config = config
                self.add_status_message(f"{EMOJI['SUCCESS']} 配置保存成功")
                return {'success': True, 'message': '配置保存成功'}
            else:
                return {'success': False, 'error': '保存配置失败'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def generate_email(self):
        """生成随机邮箱"""
        try:
            email = generate_random_email(self.add_status_message)
            if email:
                return {'success': True, 'email': email}
            else:
                return {'success': False, 'error': '生成邮箱失败'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def auto_login(self, data):
        """一键登录"""
        try:
            login_email = data.get('login_email', '')
            
            config = get_config()
            if not config:
                return {'success': False, 'error': '无法获取配置'}
            
            email_prefix = config.get('Email', 'prefix', fallback='')
            email_password = config.get('Email', 'password', fallback='')
            
            if not email_prefix or not email_password:
                return {'success': False, 'error': '请先设置邮箱凭据'}
            
            monitoring_email = f"{email_prefix}@2925.com"
            
            # 在后台线程中执行登录
            def login_task():
                try:
                    run_auto_login_flow(
                        monitoring_email, 
                        login_email, 
                        email_password, 
                        self.add_status_message
                    )
                except Exception as e:
                    self.add_status_message(f"登录过程中发生错误: {e}")
            
            threading.Thread(target=login_task, daemon=True).start()
            return {'success': True, 'message': '登录流程已启动'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def reset_environment(self):
        """重置环境"""
        try:
            def reset_task():
                try:
                    run_full_reset_flow(self.add_status_message)
                except Exception as e:
                    self.add_status_message(f"重置过程中发生错误: {e}")
            
            threading.Thread(target=reset_task, daemon=True).start()
            return {'success': True, 'message': '环境重置已启动'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def open_email_client(self):
        """打开邮箱客户端"""
        try:
            config = get_config()
            if not config:
                return {'success': False, 'error': '无法获取配置'}
            
            email_prefix = config.get('Email', 'prefix', fallback='')
            email_password = config.get('Email', 'password', fallback='')
            
            if not email_prefix or not email_password:
                return {'success': False, 'error': '请先设置邮箱凭据'}
            
            email_to_watch = f"{email_prefix}@2925.com"
            
            def email_task():
                try:
                    launch_email_client_process(
                        email_to_watch, 
                        email_password, 
                        self.add_status_message
                    )
                except Exception as e:
                    self.add_status_message(f"邮箱客户端启动失败: {e}")
            
            threading.Thread(target=email_task, daemon=True).start()
            return {'success': True, 'message': '邮箱客户端已启动'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_status_messages(self):
        """获取状态消息"""
        try:
            messages = self.status_messages.copy()
            self.status_messages.clear()  # 清空已读消息
            return {'success': True, 'messages': messages}
        except Exception as e:
            return {'success': False, 'error': str(e)}

def create_local_gui():
    """创建本地界面"""
    try:
        import webview
        
        safe_print("[INFO] 启动续杯工具 - 纯本地界面")

        # 创建API实例
        api = LocalInterfaceAPI()

        # 获取HTML文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        html_file = os.path.join(current_dir, 'index.html')

        if not os.path.exists(html_file):
            safe_print(f"[ERROR] HTML文件不存在: {html_file}")
            return False

        safe_print(f"[DEBUG] HTML文件路径: {html_file}")
        
        # 设置webview全局设置（使用最简配置）
        try:
            webview.settings['ALLOW_DOWNLOADS'] = False
            webview.settings['ALLOW_FILE_URLS'] = True
            webview.settings['OPEN_EXTERNAL_LINKS_IN_BROWSER'] = True
            webview.settings['OPEN_DEVTOOLS_IN_DEBUG'] = False
            # 禁用HTTP服务器相关设置
            webview.settings['ALLOW_REMOTE_DEBUGGING'] = False
            webview.settings['ALLOW_INSECURE_CONTENT'] = False
        except Exception as e:
            safe_print(f"[WARNING] 设置webview配置时出错: {e}")
            # 继续执行，使用默认设置
        
        # 确保使用绝对路径
        html_file = os.path.abspath(html_file)
        safe_print(f"[DEBUG] 使用HTML文件: {html_file}")

        # 创建webview窗口（使用最简配置避免HTTP服务器）
        window = webview.create_window(
            title='Cursor管理工具',
            url=html_file,  # 直接使用绝对路径
            js_api=api,     # 暴露API给JavaScript
            width=900,     # 调整窗口宽度
            height=700,     # 调整窗口高度
            min_size=(300, 300),  # 调整最小尺寸
            resizable=True,
            text_select=False,
            background_color='#667eea'
        )
        
        safe_print("[INFO] 启动界面窗口...")

        # 启动webview（使用最简配置）
        safe_print("[INFO] 启动webview，如果出现端口弹窗请点击'确定'")
        webview.start(debug=False)

        return True

    except ImportError as e:
        safe_print(f"[ERROR] 缺少依赖库: {e}")
        safe_print("📦 请运行: pip install pywebview")
        return False
    except Exception as e:
        safe_print(f"[ERROR] 启动本地界面失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        safe_print("🚀 启动续杯工具 - 纯本地界面版")
        safe_print("📋 这个版本不需要服务器，直接使用本地HTML文件")

        if not create_local_gui():
            safe_print("❌ 启动失败")
            input("按回车键退出...")

    except KeyboardInterrupt:
        safe_print("\n👋 用户取消，程序退出")
    except Exception as e:
        safe_print(f"❌ 启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
