package web

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Server represents the web server
type Server struct {
	config     *config.Config
	logger     *logger.Logger
	router     *gin.Engine
	httpServer *http.Server
	handlers   *Handlers
}

// NewServer creates a new web server
func NewServer(cfg *config.Config) *Server {
	// Set Gin mode based on debug setting
	if !cfg.Server.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	router.Use(securityMiddleware())

	server := &Server{
		config: cfg,
		logger: logger.Get(),
		router: router,
		handlers: NewHandlers(cfg),
	}

	server.setupRoutes()
	
	return server
}

// Start starts the web server
func (s *Server) Start() error {
	address := fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)
	
	s.httpServer = &http.Server{
		Addr:         address,
		Handler:      s.router,
		ReadTimeout:  time.Duration(s.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(s.config.Server.WriteTimeout) * time.Second,
	}

	s.logger.Infof("Starting web server on %s", address)
	s.logger.Infof("Debug mode: %v", s.config.Server.Debug)

	if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start server: %w", err)
	}

	return nil
}

// Stop stops the web server gracefully
func (s *Server) Stop(ctx context.Context) error {
	s.logger.Info("Stopping web server...")
	
	if s.httpServer != nil {
		if err := s.httpServer.Shutdown(ctx); err != nil {
			return fmt.Errorf("failed to shutdown server: %w", err)
		}
	}
	
	s.logger.Info("Web server stopped")
	return nil
}

// setupRoutes sets up all the routes
func (s *Server) setupRoutes() {
	// Serve static files
	s.router.Static("/static", s.config.Server.StaticDir)
	s.router.StaticFile("/", "internal/web/static/index.html")
	s.router.StaticFile("/favicon.ico", "internal/web/static/favicon.ico")

	// API routes
	api := s.router.Group("/api/v1")
	{
		// Configuration endpoints
		config := api.Group("/config")
		{
			config.GET("", s.handlers.GetConfig)
			config.POST("", s.handlers.SaveConfig)
		}

		// Email endpoints
		email := api.Group("/email")
		{
			email.GET("/generate", s.handlers.GenerateEmail)
			email.POST("/test", s.handlers.TestEmailConnection)
			email.POST("/monitor/start", s.handlers.StartEmailMonitoring)
			email.POST("/monitor/stop", s.handlers.StopEmailMonitoring)
			email.GET("/monitor/status", s.handlers.GetEmailMonitorStatus)
		}

		// Cursor management endpoints
		cursor := api.Group("/cursor")
		{
			cursor.GET("/status", s.handlers.GetCursorStatus)
			cursor.POST("/reset", s.handlers.ResetEnvironment)
			cursor.POST("/restart", s.handlers.RestartCursor)
			cursor.POST("/launch", s.handlers.LaunchCursor)
			cursor.POST("/terminate", s.handlers.TerminateCursor)
		}

		// Browser automation endpoints
		browser := api.Group("/browser")
		{
			browser.POST("/login", s.handlers.AutoLogin)
			browser.POST("/quick-login", s.handlers.QuickLogin)
			browser.GET("/current-url", s.handlers.GetCurrentURL)
			browser.POST("/screenshot", s.handlers.TakeScreenshot)
		}

		// System endpoints
		system := api.Group("/system")
		{
			system.GET("/info", s.handlers.GetSystemInfo)
			system.GET("/health", s.handlers.HealthCheck)
		}

		// WebSocket endpoint for real-time updates
		api.GET("/ws", s.handlers.WebSocketHandler)
	}

	// Health check endpoint (outside API group)
	s.router.GET("/health", s.handlers.HealthCheck)

	// Catch-all route for SPA
	s.router.NoRoute(func(c *gin.Context) {
		c.File("internal/web/static/index.html")
	})
}

// GetRouter returns the Gin router (for testing)
func (s *Server) GetRouter() *gin.Engine {
	return s.router
}

// corsMiddleware adds CORS headers
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// securityMiddleware adds security headers
func securityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		
		// Only add HSTS for HTTPS
		if c.Request.TLS != nil {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}

		c.Next()
	}
}

// rateLimitMiddleware adds rate limiting (basic implementation)
func rateLimitMiddleware() gin.HandlerFunc {
	// This is a simple rate limiter - in production, use a proper rate limiting library
	clients := make(map[string]time.Time)
	
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()
		
		if lastRequest, exists := clients[clientIP]; exists {
			if now.Sub(lastRequest) < time.Second {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"error": "Rate limit exceeded",
				})
				c.Abort()
				return
			}
		}
		
		clients[clientIP] = now
		c.Next()
	}
}

// loggingMiddleware adds request logging
func loggingMiddleware(logger *logger.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format(time.RFC1123),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

// authMiddleware adds authentication (placeholder for future implementation)
func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement authentication if needed
		// For now, this is a local application, so no auth required
		c.Next()
	}
}

// GetAddress returns the server address
func (s *Server) GetAddress() string {
	return fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)
}

// GetURL returns the full server URL
func (s *Server) GetURL() string {
	return fmt.Sprintf("http://%s", s.GetAddress())
}
