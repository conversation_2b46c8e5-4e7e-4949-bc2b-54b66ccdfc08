package cursor

import (
	"fmt"
	"runtime"
	"time"

	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"
)

// Manager provides high-level Cursor management operations
type Manager struct {
	paths           *Paths
	processManager  *ProcessManager
	machineResetter *MachineIDResetter
	logger          *logger.Logger
}

// NewManager creates a new Cursor manager
func NewManager() (*Manager, error) {
	paths, err := GetCursorPaths()
	if err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to get Cursor paths: %w", err)
	}

	processManager, err := NewProcessManager()
	if err != nil {
		return nil, fmt.Errorf("failed to create process manager: %w", err)
	}

	machineResetter, err := NewMachineIDResetter()
	if err != nil {
		return nil, fmt.Errorf("failed to create machine ID resetter: %w", err)
	}

	return &Manager{
		paths:           paths,
		processManager:  processManager,
		machineResetter: machineResetter,
		logger:          logger.Get(),
	}, nil
}

// FullReset performs a complete environment reset
func (m *Manager) FullReset() error {
	startTime := time.Now()
	m.logger.Infof("%s Starting environment reset flow - %s", utils.GetEmoji("rocket"), startTime.Format("2006-01-02 15:04:05"))
	m.logger.Infof("%s Operating System: %s %s", utils.GetEmoji("computer"), runtime.GOOS, runtime.GOARCH)
	m.logger.Info("=" + utils.RepeatString("=", 59))

	// Step 1: Terminate processes
	m.logger.Info(utils.FormatStep(1, "Terminate Cursor-related processes"))
	m.logger.Info(utils.RepeatString("-", 40))

	processesKilled, err := m.processManager.TerminateAll()
	if err != nil {
		m.logger.Error(utils.FormatError("Failed to terminate Cursor processes: " + err.Error()))
		return fmt.Errorf("failed to terminate processes: %w", err)
	}

	m.logger.Infof("%s Step 1 completed: Terminated %d processes", utils.GetEmoji("success"), processesKilled)
	m.logger.Info("")

	// Step 2: Check privileges and reset machine ID
	m.logger.Info(utils.FormatStep(2, "Reset machine ID"))
	m.logger.Info(utils.RepeatString("-", 40))

	// Check administrator privileges
	m.logger.Info(utils.FormatInfo("Checking administrator privileges..."))
	if runtime.GOOS == "windows" && !utils.IsAdmin() {
		m.logger.Error(utils.FormatError("Insufficient privileges! Machine ID reset requires administrator rights."))
		m.logger.Info(utils.FormatInfo("Please right-click the program and select 'Run as administrator'"))
		return fmt.Errorf("administrator privileges required")
	} else {
		m.logger.Info(utils.FormatSuccess("Administrator privilege check passed"))
	}

	// Display Cursor path information
	m.logger.Infof("%s Cursor data path: %s", utils.GetEmoji("folder"), m.paths.DataPath)
	m.logger.Infof("%s Machine ID file path: %s", utils.GetEmoji("folder"), m.paths.MachineIDPath)
	m.logger.Infof("%s Application path: %s", utils.GetEmoji("folder"), m.paths.AppPath)

	m.logger.Info(utils.FormatInfo("Starting machine ID reset..."))
	time.Sleep(1 * time.Second)

	if err := m.machineResetter.Reset(); err != nil {
		m.logger.Error(utils.FormatError("Machine ID reset failed: " + err.Error()))
		return fmt.Errorf("machine ID reset failed: %w", err)
	}

	m.logger.Info(utils.FormatSuccess("Step 2 completed: Machine ID reset successful"))
	m.logger.Info("")

	// Step 3: Restart application
	m.logger.Info(utils.FormatStep(3, "Restart Cursor"))
	m.logger.Info(utils.RepeatString("-", 40))

	m.logger.Info(utils.FormatLoading("Waiting for system to stabilize..."))
	time.Sleep(2 * time.Second)

	if err := m.processManager.Launch(); err != nil {
		m.logger.Warn(utils.FormatWarning("Step 3 warning: Cursor launch may have failed, please start manually"))
		m.logger.Warnf("Launch error: %v", err)
	} else {
		m.logger.Info(utils.FormatSuccess("Step 3 completed: Cursor launch successful"))
	}

	// Completion summary
	endTime := time.Now()
	duration := endTime.Sub(startTime)
	m.logger.Info("")
	m.logger.Info("=" + utils.RepeatString("=", 59))
	m.logger.Info(utils.FormatSuccess("Environment reset flow completed!"))
	m.logger.Infof("%s Total time: %.2f seconds", utils.GetEmoji("timer"), duration.Seconds())
	m.logger.Infof("%s Completion time: %s", utils.GetEmoji("clock"), endTime.Format("2006-01-02 15:04:05"))
	m.logger.Info(utils.FormatInfo("Suggestion: Please restart Cursor to ensure all changes take effect"))

	return nil
}

// QuickReset performs a quick machine ID reset without process management
func (m *Manager) QuickReset() error {
	m.logger.Info(utils.FormatInfo("Starting quick machine ID reset..."))
	
	if err := m.machineResetter.Reset(); err != nil {
		return fmt.Errorf("quick reset failed: %w", err)
	}
	
	m.logger.Info(utils.FormatSuccess("Quick reset completed successfully"))
	return nil
}

// RestartCursor terminates and restarts Cursor
func (m *Manager) RestartCursor() error {
	m.logger.Info(utils.FormatInfo("Restarting Cursor..."))
	
	// Terminate existing processes
	processesKilled, err := m.processManager.TerminateAll()
	if err != nil {
		return fmt.Errorf("failed to terminate Cursor processes: %w", err)
	}
	
	m.logger.Infof("Terminated %d processes", processesKilled)
	
	// Wait for processes to fully terminate
	if err := m.processManager.WaitForTermination(10 * time.Second); err != nil {
		m.logger.Warn("Timeout waiting for processes to terminate, proceeding anyway...")
	}
	
	// Launch Cursor
	if err := m.processManager.Launch(); err != nil {
		return fmt.Errorf("failed to launch Cursor: %w", err)
	}
	
	m.logger.Info(utils.FormatSuccess("Cursor restart completed"))
	return nil
}

// GetStatus returns the current status of Cursor
func (m *Manager) GetStatus() (*Status, error) {
	status := &Status{
		Paths: m.paths,
	}
	
	// Check if Cursor is running
	running, err := m.processManager.IsRunning()
	if err != nil {
		return nil, fmt.Errorf("failed to check if Cursor is running: %w", err)
	}
	status.IsRunning = running
	
	// Get process information
	processInfo, err := m.processManager.GetProcessInfo()
	if err != nil {
		return nil, fmt.Errorf("failed to get process info: %w", err)
	}
	status.ProcessInfo = processInfo
	
	// Validate paths
	status.PathsValid = m.paths.Validate() == nil
	
	// Check if database exists
	status.DatabaseExists = utils.FileExists(m.paths.DBPath)
	
	// Check if machine ID file exists
	status.MachineIDExists = utils.FileExists(m.paths.MachineIDPath)
	
	return status, nil
}

// ValidateEnvironment checks if the Cursor environment is properly set up
func (m *Manager) ValidateEnvironment() error {
	m.logger.Info(utils.FormatInfo("Validating Cursor environment..."))
	
	// Check paths
	if err := m.paths.Validate(); err != nil {
		return fmt.Errorf("path validation failed: %w", err)
	}
	
	m.logger.Info(utils.FormatSuccess("Environment validation passed"))
	return nil
}

// Status represents the current status of Cursor
type Status struct {
	IsRunning       bool              `json:"is_running"`
	ProcessInfo     map[string][]int  `json:"process_info"`
	Paths           *Paths            `json:"paths"`
	PathsValid      bool              `json:"paths_valid"`
	DatabaseExists  bool              `json:"database_exists"`
	MachineIDExists bool              `json:"machine_id_exists"`
}
