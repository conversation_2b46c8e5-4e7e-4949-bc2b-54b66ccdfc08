package email

import (
	"context"
	"fmt"
	"sync"
	"time"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"
)

// Monitor handles email monitoring for verification codes
type Monitor struct {
	client       *Client
	config       *config.Config
	logger       *logger.Logger
	isRunning    bool
	mu           sync.RWMutex
	stopChan     chan struct{}
	code<PERSON>han     chan string
	statusChan   chan string
	lastCheck    time.Time
	checkCount   int
}

// MonitorConfig represents monitoring configuration
type MonitorConfig struct {
	Email           string        `json:"email"`
	Password        string        `json:"password"`
	CheckInterval   time.Duration `json:"check_interval"`
	Timeout         time.Duration `json:"timeout"`
	MaxChecks       int           `json:"max_checks"`
	DeleteAfterRead bool          `json:"delete_after_read"`
}

// NewMonitor creates a new email monitor
func NewMonitor(cfg *config.Config) *Monitor {
	return &Monitor{
		config:     cfg,
		logger:     logger.Get(),
		stop<PERSON>han:   make(chan struct{}),
		code<PERSON>han:   make(chan string, 1),
		status<PERSON>han: make(chan string, 10),
	}
}

// Start starts monitoring the specified email for verification codes
func (m *Monitor) Start(ctx context.Context, monitorConfig *MonitorConfig) error {
	m.mu.Lock()
	if m.isRunning {
		m.mu.Unlock()
		return fmt.Errorf("monitor is already running")
	}
	m.isRunning = true
	m.mu.Unlock()

	// Create email client
	m.client = NewPOP3Client(monitorConfig.Email, monitorConfig.Password)

	// Test connection first
	m.sendStatus(utils.FormatInfo("Testing email connection..."))
	if err := m.client.TestConnection(); err != nil {
		m.stop()
		return fmt.Errorf("email connection test failed: %w", err)
	}

	m.sendStatus(utils.FormatSuccess("Email connection test passed"))
	m.sendStatus(utils.FormatInfo(fmt.Sprintf("Starting monitoring for: %s", monitorConfig.Email)))
	m.sendStatus(utils.FormatInfo(fmt.Sprintf("Check interval: %v", monitorConfig.CheckInterval)))
	m.sendStatus(utils.FormatInfo(fmt.Sprintf("Timeout: %v", monitorConfig.Timeout)))

	// Start monitoring goroutine
	go m.monitorLoop(ctx, monitorConfig)

	return nil
}

// Stop stops the email monitoring
func (m *Monitor) Stop() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.stop()
}

// stop is the internal stop method (must be called with lock held)
func (m *Monitor) stop() {
	if m.isRunning {
		m.isRunning = false
		close(m.stopChan)
		m.sendStatus(utils.FormatInfo("Email monitoring stopped"))
	}
}

// IsRunning returns whether the monitor is currently running
func (m *Monitor) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isRunning
}

// GetCodeChannel returns the channel for receiving verification codes
func (m *Monitor) GetCodeChannel() <-chan string {
	return m.codeChan
}

// GetStatusChannel returns the channel for receiving status updates
func (m *Monitor) GetStatusChannel() <-chan string {
	return m.statusChan
}

// WaitForCode waits for a verification code with timeout
func (m *Monitor) WaitForCode(timeout time.Duration) (string, error) {
	select {
	case code := <-m.codeChan:
		return code, nil
	case <-time.After(timeout):
		return "", fmt.Errorf("timeout waiting for verification code")
	}
}

// monitorLoop is the main monitoring loop
func (m *Monitor) monitorLoop(ctx context.Context, config *MonitorConfig) {
	defer m.stop()

	ticker := time.NewTicker(config.CheckInterval)
	defer ticker.Stop()

	startTime := time.Now()
	m.lastCheck = startTime

	for {
		select {
		case <-ctx.Done():
			m.sendStatus(utils.FormatInfo("Monitoring cancelled by context"))
			return

		case <-m.stopChan:
			m.sendStatus(utils.FormatInfo("Monitoring stopped"))
			return

		case <-ticker.C:
			m.checkCount++
			elapsed := time.Since(startTime)

			// Check timeout
			if elapsed > config.Timeout {
				m.sendStatus(utils.FormatError(fmt.Sprintf("Monitoring timeout after %v", elapsed)))
				return
			}

			// Check max checks
			if config.MaxChecks > 0 && m.checkCount > config.MaxChecks {
				m.sendStatus(utils.FormatError(fmt.Sprintf("Maximum checks (%d) exceeded", config.MaxChecks)))
				return
			}

			// Update status every 15 seconds
			if m.checkCount%3 == 0 { // Assuming 5-second intervals
				m.sendStatus(utils.FormatInfo(fmt.Sprintf("Waiting for verification code... (%.0fs elapsed)", elapsed.Seconds())))
			}

			// Check for new emails
			if err := m.checkForVerificationCode(config); err != nil {
				m.logger.Warnf("Error checking for verification code: %v", err)
				// Continue monitoring despite errors
			}

			m.lastCheck = time.Now()
		}
	}
}

// checkForVerificationCode checks for new emails and extracts verification codes
func (m *Monitor) checkForVerificationCode(config *MonitorConfig) error {
	conn, err := m.client.Connect()
	if err != nil {
		return fmt.Errorf("failed to connect: %w", err)
	}
	defer conn.Close()

	if err := m.client.Authenticate(conn); err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	messageCount, err := m.client.GetMessageCount(conn)
	if err != nil {
		return fmt.Errorf("failed to get message count: %w", err)
	}

	if messageCount == 0 {
		return nil // No messages
	}

	// Check recent messages (last 5 or all if fewer)
	startMsg := messageCount - 4
	if startMsg < 1 {
		startMsg = 1
	}

	for msgNum := messageCount; msgNum >= startMsg; msgNum-- {
		content, err := m.client.RetrieveMessage(conn, msgNum)
		if err != nil {
			m.logger.Warnf("Failed to retrieve message %d: %v", msgNum, err)
			continue
		}

		// Extract verification code
		code, err := m.client.ExtractVerificationCode(content)
		if err != nil {
			continue // No code found in this message
		}

		// Found a verification code!
		m.sendStatus(utils.FormatSuccess(fmt.Sprintf("Verification code found: %s", code)))

		// Delete message if configured
		if config.DeleteAfterRead {
			if err := m.client.DeleteMessage(conn, msgNum); err != nil {
				m.logger.Warnf("Failed to delete message %d: %v", msgNum, err)
			}
		}

		// Send code to channel
		select {
		case m.codeChan <- code:
			m.sendStatus(utils.FormatSuccess("Verification code sent to application"))
		default:
			m.logger.Warn("Code channel is full, dropping code")
		}

		// Quit gracefully
		if err := m.client.Quit(conn); err != nil {
			m.logger.Warnf("Failed to quit gracefully: %v", err)
		}

		return nil
	}

	// Quit gracefully
	if err := m.client.Quit(conn); err != nil {
		m.logger.Warnf("Failed to quit gracefully: %v", err)
	}

	return nil
}

// sendStatus sends a status message to the status channel
func (m *Monitor) sendStatus(message string) {
	select {
	case m.statusChan <- message:
	default:
		// Channel is full, drop the message
		m.logger.Warn("Status channel is full, dropping message: " + message)
	}
}

// GetStats returns monitoring statistics
func (m *Monitor) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return map[string]interface{}{
		"is_running":   m.isRunning,
		"check_count":  m.checkCount,
		"last_check":   m.lastCheck,
		"uptime":       time.Since(m.lastCheck),
	}
}

// DefaultMonitorConfig returns a default monitoring configuration
func DefaultMonitorConfig(email, password string) *MonitorConfig {
	return &MonitorConfig{
		Email:           email,
		Password:        password,
		CheckInterval:   5 * time.Second,
		Timeout:         90 * time.Second,
		MaxChecks:       18, // 90 seconds / 5 seconds
		DeleteAfterRead: false,
	}
}
