package browser

import (
	"context"
	"fmt"
	"strings"
	"time"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"
)

// Automation handles browser automation tasks
type Automation struct {
	chrome *Chrome
	config *config.Config
	logger *logger.Logger
}

// NewAutomation creates a new browser automation instance
func NewAutomation(cfg *config.Config) *Automation {
	return &Automation{
		chrome: NewChrome(cfg),
		config: cfg,
		logger: logger.Get(),
	}
}

// Start starts the browser automation
func (a *Automation) Start() error {
	return a.chrome.Start()
}

// Stop stops the browser automation
func (a *Automation) Stop() {
	a.chrome.Stop()
}

// AutoLogin performs automated login with email verification
func (a *Automation) AutoLogin(ctx context.Context, targetURL, email string, codeChannel <-chan string) error {
	a.logger.Info(utils.FormatStep(1, "Starting automated login process"))

	// Step 1: Navigate to target URL
	if err := a.navigateToTarget(targetURL); err != nil {
		return fmt.Errorf("navigation failed: %w", err)
	}

	// Step 2: Fill email and submit
	if err := a.fillEmailAndSubmit(email); err != nil {
		return fmt.Errorf("email submission failed: %w", err)
	}

	// Step 3: Click email sign-in code button
	if err := a.clickEmailSignInCode(); err != nil {
		return fmt.Errorf("email sign-in code click failed: %w", err)
	}

	// Step 4: Wait for verification code and fill it
	if err := a.waitAndFillVerificationCode(ctx, codeChannel); err != nil {
		return fmt.Errorf("verification code handling failed: %w", err)
	}

	a.logger.Info(utils.FormatSuccess("Automated login process completed"))
	return nil
}

// navigateToTarget navigates to the target URL
func (a *Automation) navigateToTarget(targetURL string) error {
	a.logger.Info(utils.FormatInfo("Step 1: Navigating to target URL"))

	// Ensure URL has protocol
	if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
		targetURL = "https://" + targetURL
	}

	a.logger.Infof("Loading URL: %s", targetURL)

	if err := a.chrome.Navigate(targetURL); err != nil {
		return fmt.Errorf("failed to navigate to %s: %w", targetURL, err)
	}

	// Wait for page to load
	if err := a.chrome.WaitForNavigation(15 * time.Second); err != nil {
		a.logger.Warn("Navigation timeout, but continuing...")
	}

	a.logger.Info(utils.FormatSuccess("Navigation completed"))
	return nil
}

// fillEmailAndSubmit fills the email field and submits the form
func (a *Automation) fillEmailAndSubmit(email string) error {
	a.logger.Info(utils.FormatInfo("Step 2: Filling email and clicking 'Continue'"))

	// Wait for email input field
	emailSelector := `input[name="email"]`
	if err := a.chrome.WaitForElement(emailSelector, 10*time.Second); err != nil {
		return fmt.Errorf("email input field not found: %w", err)
	}

	// Fill email
	a.logger.Infof("Filling email: %s", email)
	if err := a.chrome.InputText(emailSelector, email); err != nil {
		return fmt.Errorf("failed to fill email: %w", err)
	}

	// Add random delay to simulate human behavior
	a.chrome.AddRandomDelay(500, 1500)

	// Find and click continue button
	continueSelectors := []string{
		`input[type="submit"]`,
		`button[type="submit"]`,
		`button:contains("Continue")`,
		`input[value*="Continue"]`,
		`.continue-button`,
		`#continue`,
	}

	var continueErr error
	for _, selector := range continueSelectors {
		if err := a.chrome.ClickElement(selector); err != nil {
			continueErr = err
			continue
		}
		continueErr = nil
		break
	}

	if continueErr != nil {
		return fmt.Errorf("failed to find or click continue button: %w", continueErr)
	}

	a.logger.Info(utils.FormatSuccess("Email submitted, waiting for next page"))

	// Wait for navigation
	if err := a.chrome.WaitForNavigation(10 * time.Second); err != nil {
		a.logger.Warn("Navigation timeout after email submission, but continuing...")
	}

	return nil
}

// clickEmailSignInCode clicks the "Email sign-in code" button
func (a *Automation) clickEmailSignInCode() error {
	a.logger.Info(utils.FormatInfo("Step 3: Waiting for password page and clicking verification code login"))

	// Wait for the page to load and look for email sign-in code button
	emailCodeSelectors := []string{
		`button:contains("Email sign-in code")`,
		`a:contains("Email sign-in code")`,
		`[data-testid*="email-code"]`,
		`.email-code-button`,
		`button[aria-label*="email"]`,
		`*:contains("Email sign-in code")`,
	}

	var clickErr error
	for _, selector := range emailCodeSelectors {
		// Wait for element with shorter timeout for each selector
		if err := a.chrome.WaitForElement(selector, 5*time.Second); err != nil {
			continue
		}

		if err := a.chrome.ClickElement(selector); err != nil {
			clickErr = err
			continue
		}

		clickErr = nil
		break
	}

	if clickErr != nil {
		return fmt.Errorf("failed to find or click 'Email sign-in code' button: %w", clickErr)
	}

	a.logger.Info(utils.FormatSuccess("'Email sign-in code' button clicked"))

	// Wait for verification code page
	if err := a.chrome.WaitForNavigation(10 * time.Second); err != nil {
		a.logger.Warn("Navigation timeout after clicking email code button, but continuing...")
	}

	return nil
}

// waitAndFillVerificationCode waits for verification code and fills it
func (a *Automation) waitAndFillVerificationCode(ctx context.Context, codeChannel <-chan string) error {
	a.logger.Info(utils.FormatInfo("Step 4: Starting verification code listener"))

	// Wait for verification code with timeout
	select {
	case code := <-codeChannel:
		a.logger.Infof("Received verification code: %s", code)
		return a.fillVerificationCode(code)

	case <-ctx.Done():
		return fmt.Errorf("context cancelled while waiting for verification code")

	case <-time.After(90 * time.Second):
		a.logger.Error(utils.FormatError("Timeout waiting for verification code"))
		a.logger.Info(utils.FormatInfo("Please manually enter the verification code to complete login"))
		a.logger.Info(utils.FormatInfo("Note: Please manually close this browser window after completion"))
		return fmt.Errorf("verification code timeout")
	}
}

// fillVerificationCode fills the verification code into the form
func (a *Automation) fillVerificationCode(code string) error {
	a.logger.Info(utils.FormatInfo("Step 5: Successfully received verification code, auto-filling"))

	// Look for verification code input fields
	// Some sites use individual inputs for each digit, others use a single input
	
	// Try single input field first
	singleInputSelectors := []string{
		`input[name*="code"]`,
		`input[id*="code"]`,
		`input[placeholder*="code"]`,
		`.verification-code input`,
		`#verification-code`,
		`input[type="text"][maxlength="6"]`,
		`input[type="text"][maxlength="4"]`,
		`input[type="text"][maxlength="8"]`,
	}

	for _, selector := range singleInputSelectors {
		if err := a.chrome.WaitForElement(selector, 2*time.Second); err != nil {
			continue
		}

		if err := a.chrome.InputText(selector, code); err != nil {
			continue
		}

		a.logger.Info(utils.FormatSuccess("Verification code filled in single input"))
		return a.finalizeLogin()
	}

	// Try individual digit inputs
	if err := a.fillIndividualDigits(code); err != nil {
		return fmt.Errorf("failed to fill verification code: %w", err)
	}

	return a.finalizeLogin()
}

// fillIndividualDigits fills verification code into individual digit inputs
func (a *Automation) fillIndividualDigits(code string) error {
	a.logger.Info("Attempting to fill individual digit inputs")

	for i, digit := range code {
		selector := fmt.Sprintf(`input[data-index="%d"]`, i)
		
		if err := a.chrome.WaitForElement(selector, 2*time.Second); err != nil {
			// Try alternative selectors
			altSelectors := []string{
				fmt.Sprintf(`.digit-input:nth-child(%d)`, i+1),
				fmt.Sprintf(`.code-input:nth-child(%d)`, i+1),
				fmt.Sprintf(`input:nth-of-type(%d)`, i+1),
			}

			found := false
			for _, altSelector := range altSelectors {
				if err := a.chrome.WaitForElement(altSelector, 1*time.Second); err != nil {
					continue
				}
				selector = altSelector
				found = true
				break
			}

			if !found {
				return fmt.Errorf("could not find input field for digit %d", i)
			}
		}

		if err := a.chrome.InputText(selector, string(digit)); err != nil {
			return fmt.Errorf("failed to input digit %d: %w", i, err)
		}

		// Add small delay between digits
		a.chrome.AddRandomDelay(100, 300)
	}

	a.logger.Info(utils.FormatSuccess("Verification code filled in individual inputs"))
	return nil
}

// finalizeLogin completes the login process
func (a *Automation) finalizeLogin() error {
	a.logger.Info(utils.FormatSuccess("Verification code filling completed"))

	// Look for submit button (some forms auto-submit)
	submitSelectors := []string{
		`button[type="submit"]`,
		`input[type="submit"]`,
		`button:contains("Submit")`,
		`button:contains("Verify")`,
		`button:contains("Continue")`,
		`.submit-button`,
		`#submit`,
	}

	// Try to click submit button with short timeout
	for _, selector := range submitSelectors {
		if err := a.chrome.WaitForElement(selector, 2*time.Second); err != nil {
			continue
		}

		if err := a.chrome.ClickElement(selector); err != nil {
			continue
		}

		a.logger.Info("Submit button clicked")
		break
	}

	// Wait for potential navigation
	if err := a.chrome.WaitForNavigation(10 * time.Second); err != nil {
		a.logger.Info("No navigation detected, form may have auto-submitted")
	}

	a.logger.Info(utils.FormatSuccess("Success: Verification code has been auto-filled"))
	a.logger.Info(utils.FormatInfo("If the page doesn't auto-redirect, please manually complete the final steps"))
	a.logger.Info(utils.FormatInfo("Please manually close this browser window after completion"))

	return nil
}

// GetCurrentURL returns the current page URL
func (a *Automation) GetCurrentURL() (string, error) {
	return a.chrome.GetCurrentURL()
}

// TakeScreenshot takes a screenshot for debugging
func (a *Automation) TakeScreenshot(filename string) error {
	return a.chrome.TakeScreenshot(filename)
}
