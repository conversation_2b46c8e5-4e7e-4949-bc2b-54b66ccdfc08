package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Email   EmailConfig   `mapstructure:"email" yaml:"email"`
	<PERSON>rows<PERSON> BrowserConfig `mapstructure:"browser" yaml:"browser"`
	Server  ServerConfig  `mapstructure:"server" yaml:"server"`
	Cursor  CursorConfig  `mapstructure:"cursor" yaml:"cursor"`
	Debug   bool          `mapstructure:"debug" yaml:"debug"`
}

// EmailConfig contains email-related settings
type EmailConfig struct {
	Prefix   string `mapstructure:"prefix" yaml:"prefix"`
	Password string `mapstructure:"password" yaml:"password"`
	Domain   string `mapstructure:"domain" yaml:"domain"`
	Timeout  int    `mapstructure:"timeout" yaml:"timeout"`
}

// BrowserConfig contains browser automation settings
type BrowserConfig struct {
	ChromePath string `mapstructure:"chrome_path" yaml:"chrome_path"`
	Headless   bool   `mapstructure:"headless" yaml:"headless"`
	Timeout    int    `mapstructure:"timeout" yaml:"timeout"`
	UserAgent  string `mapstructure:"user_agent" yaml:"user_agent"`
}

// ServerConfig contains web server settings
type ServerConfig struct {
	Host         string `mapstructure:"host" yaml:"host"`
	Port         int    `mapstructure:"port" yaml:"port"`
	Debug        bool   `mapstructure:"debug" yaml:"debug"`
	StaticDir    string `mapstructure:"static_dir" yaml:"static_dir"`
	TemplateDir  string `mapstructure:"template_dir" yaml:"template_dir"`
	ReadTimeout  int    `mapstructure:"read_timeout" yaml:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout" yaml:"write_timeout"`
}

// CursorConfig contains Cursor application settings
type CursorConfig struct {
	AutoLaunch   bool   `mapstructure:"auto_launch" yaml:"auto_launch"`
	ResetTimeout int    `mapstructure:"reset_timeout" yaml:"reset_timeout"`
	DataPath     string `mapstructure:"data_path" yaml:"data_path"`
	AppPath      string `mapstructure:"app_path" yaml:"app_path"`
}

var (
	defaultConfig *Config
	configPath    string
)

// GetDefaultConfig returns the default configuration
func GetDefaultConfig() *Config {
	return &Config{
		Email: EmailConfig{
			Prefix:  "",
			Password: "",
			Domain:  "2925.com",
			Timeout: 90,
		},
		Browser: BrowserConfig{
			ChromePath: getDefaultChromePath(),
			Headless:   false,
			Timeout:    30,
			UserAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		},
		Server: ServerConfig{
			Host:         "localhost",
			Port:         8080,
			Debug:        false,
			StaticDir:    "internal/web/static",
			TemplateDir:  "internal/web/templates",
			ReadTimeout:  30,
			WriteTimeout: 30,
		},
		Cursor: CursorConfig{
			AutoLaunch:   true,
			ResetTimeout: 60,
			DataPath:     getDefaultCursorDataPath(),
			AppPath:      getDefaultCursorAppPath(),
		},
		Debug: false,
	}
}

// Load loads configuration from file
func Load(configFile string) (*Config, error) {
	config := GetDefaultConfig()
	
	if configFile == "" {
		configFile = getDefaultConfigPath()
	}
	
	configPath = configFile
	
	// Set up viper
	viper.SetConfigFile(configFile)
	viper.SetConfigType("yaml")
	
	// Set environment variable prefix
	viper.SetEnvPrefix("CURSOR_MANAGER")
	viper.AutomaticEnv()
	
	// Create config directory if it doesn't exist
	configDir := filepath.Dir(configFile)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %w", err)
	}
	
	// Read config file if it exists
	if _, err := os.Stat(configFile); err == nil {
		if err := viper.ReadInConfig(); err != nil {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		
		if err := viper.Unmarshal(config); err != nil {
			return nil, fmt.Errorf("failed to unmarshal config: %w", err)
		}
	} else {
		// Create default config file
		if err := Save(config); err != nil {
			return nil, fmt.Errorf("failed to create default config: %w", err)
		}
	}
	
	defaultConfig = config
	return config, nil
}

// Save saves configuration to file
func Save(config *Config) error {
	if configPath == "" {
		configPath = getDefaultConfigPath()
	}
	
	// Create config directory if it doesn't exist
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}
	
	// Set all values in viper
	viper.Set("email", config.Email)
	viper.Set("browser", config.Browser)
	viper.Set("server", config.Server)
	viper.Set("cursor", config.Cursor)
	viper.Set("debug", config.Debug)
	
	// Write config file
	if err := viper.WriteConfigAs(configPath); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}
	
	defaultConfig = config
	return nil
}

// Get returns the current configuration
func Get() *Config {
	if defaultConfig == nil {
		defaultConfig = GetDefaultConfig()
	}
	return defaultConfig
}

// getDefaultConfigPath returns the default configuration file path
func getDefaultConfigPath() string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "config.yaml"
	}
	
	configDir := filepath.Join(homeDir, ".cursor-manager")
	return filepath.Join(configDir, "config.yaml")
}

// getDefaultChromePath returns the default Chrome executable path
func getDefaultChromePath() string {
	switch runtime.GOOS {
	case "windows":
		paths := []string{
			`C:\Program Files\Google\Chrome\Application\chrome.exe`,
			`C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`,
			filepath.Join(os.Getenv("LOCALAPPDATA"), `Google\Chrome\Application\chrome.exe`),
		}
		for _, path := range paths {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
	case "darwin":
		return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
	case "linux":
		paths := []string{
			"/usr/bin/google-chrome",
			"/usr/bin/google-chrome-stable",
			"/usr/bin/chromium-browser",
			"/usr/bin/chromium",
		}
		for _, path := range paths {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
	}
	return ""
}

// getDefaultCursorDataPath returns the default Cursor data directory path
func getDefaultCursorDataPath() string {
	switch runtime.GOOS {
	case "windows":
		return filepath.Join(os.Getenv("APPDATA"), "Cursor")
	case "darwin":
		homeDir, _ := os.UserHomeDir()
		return filepath.Join(homeDir, "Library", "Application Support", "Cursor")
	case "linux":
		homeDir, _ := os.UserHomeDir()
		return filepath.Join(homeDir, ".config", "cursor")
	}
	return ""
}

// getDefaultCursorAppPath returns the default Cursor application path
func getDefaultCursorAppPath() string {
	switch runtime.GOOS {
	case "windows":
		paths := []string{
			filepath.Join(os.Getenv("LOCALAPPDATA"), "Programs", "Cursor", "Cursor.exe"),
			filepath.Join(os.Getenv("ProgramFiles"), "cursor", "Cursor.exe"),
		}
		for _, path := range paths {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
	case "darwin":
		return "/Applications/Cursor.app"
	case "linux":
		paths := []string{
			"/usr/bin/cursor",
			filepath.Join(os.Getenv("HOME"), "Applications", "Cursor.AppImage"),
		}
		for _, path := range paths {
			if _, err := os.Stat(path); err == nil {
				return path
			}
		}
	}
	return ""
}
