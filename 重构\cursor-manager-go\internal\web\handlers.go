package web

import (
	"context"
	"net/http"
	"runtime"
	"time"

	"cursor-manager-go/internal/browser"
	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/cursor"
	"cursor-manager-go/internal/email"
	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// Handlers contains all HTTP handlers
type Handlers struct {
	config       *config.Config
	logger       *logger.Logger
	cursorMgr    *cursor.Manager
	loginMgr     *browser.LoginManager
	emailGen     *email.Generator
	wsUpgrader   websocket.Upgrader
}

// Response represents a standard API response
type Response struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// NewHandlers creates new handlers
func NewHandlers(cfg *config.Config) *Handlers {
	cursorMgr, err := cursor.NewManager()
	if err != nil {
		logger.Get().Errorf("Failed to create cursor manager: %v", err)
	}

	return &Handlers{
		config:    cfg,
		logger:    logger.Get(),
		cursorMgr: cursorMgr,
		loginMgr:  browser.NewLoginManager(cfg),
		emailGen:  email.NewGenerator(cfg),
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for local app
			},
		},
	}
}

// GetConfig returns the current configuration
func (h *Handlers) GetConfig(c *gin.Context) {
	configData := map[string]interface{}{
		"email_prefix":  h.config.Email.Prefix,
		"email_password": h.config.Email.Password,
		"email_domain":  h.config.Email.Domain,
		"browser_path":  h.config.Browser.ChromePath,
		"browser_headless": h.config.Browser.Headless,
		"server_port":   h.config.Server.Port,
		"debug":         h.config.Debug,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    configData,
	})
}

// SaveConfig saves the configuration
func (h *Handlers) SaveConfig(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	// Update configuration
	if prefix, ok := req["email_prefix"].(string); ok {
		h.config.Email.Prefix = prefix
	}
	if password, ok := req["email_password"].(string); ok {
		h.config.Email.Password = password
	}
	if domain, ok := req["email_domain"].(string); ok {
		h.config.Email.Domain = domain
	}
	if browserPath, ok := req["browser_path"].(string); ok {
		h.config.Browser.ChromePath = browserPath
	}
	if headless, ok := req["browser_headless"].(bool); ok {
		h.config.Browser.Headless = headless
	}

	// Save configuration
	if err := config.Save(h.config); err != nil {
		h.logger.Errorf("Failed to save config: %v", err)
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   "Failed to save configuration",
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Configuration saved successfully",
	})
}

// GenerateEmail generates a random email
func (h *Handlers) GenerateEmail(c *gin.Context) {
	email, err := h.emailGen.GenerateRandom()
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data: map[string]string{
			"email": email,
		},
	})
}

// TestEmailConnection tests email connection
func (h *Handlers) TestEmailConnection(c *gin.Context) {
	var req struct {
		Email    string `json:"email" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	if err := h.loginMgr.TestEmailConnection(req.Email, req.Password); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Email connection test successful",
	})
}

// StartEmailMonitoring starts email monitoring
func (h *Handlers) StartEmailMonitoring(c *gin.Context) {
	var req struct {
		Email    string `json:"email" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	ctx := context.Background()
	if err := h.loginMgr.StartEmailMonitoringOnly(ctx, req.Email, req.Password); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Email monitoring started",
	})
}

// StopEmailMonitoring stops email monitoring
func (h *Handlers) StopEmailMonitoring(c *gin.Context) {
	h.loginMgr.StopEmailMonitoring()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Email monitoring stopped",
	})
}

// GetEmailMonitorStatus returns email monitoring status
func (h *Handlers) GetEmailMonitorStatus(c *gin.Context) {
	status := h.loginMgr.GetEmailMonitorStatus()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    status,
	})
}

// GetCursorStatus returns Cursor status
func (h *Handlers) GetCursorStatus(c *gin.Context) {
	if h.cursorMgr == nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   "Cursor manager not available",
		})
		return
	}

	status, err := h.cursorMgr.GetStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    status,
	})
}

// ResetEnvironment resets the Cursor environment
func (h *Handlers) ResetEnvironment(c *gin.Context) {
	if h.cursorMgr == nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   "Cursor manager not available",
		})
		return
	}

	go func() {
		if err := h.cursorMgr.FullReset(); err != nil {
			h.logger.Errorf("Environment reset failed: %v", err)
		}
	}()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Environment reset started",
	})
}

// RestartCursor restarts Cursor
func (h *Handlers) RestartCursor(c *gin.Context) {
	if h.cursorMgr == nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   "Cursor manager not available",
		})
		return
	}

	go func() {
		if err := h.cursorMgr.RestartCursor(); err != nil {
			h.logger.Errorf("Cursor restart failed: %v", err)
		}
	}()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Cursor restart initiated",
	})
}

// LaunchCursor launches Cursor
func (h *Handlers) LaunchCursor(c *gin.Context) {
	// Implementation would go here
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Cursor launch initiated",
	})
}

// TerminateCursor terminates Cursor processes
func (h *Handlers) TerminateCursor(c *gin.Context) {
	// Implementation would go here
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Cursor termination initiated",
	})
}

// AutoLogin performs automated login
func (h *Handlers) AutoLogin(c *gin.Context) {
	var req browser.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	result, err := h.loginMgr.PerformAutoLogin(ctx, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    result,
	})
}

// QuickLogin performs quick login
func (h *Handlers) QuickLogin(c *gin.Context) {
	var req struct {
		TargetURL string `json:"target_url" binding:"required"`
		Email     string `json:"email" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Error:   "Invalid request format",
		})
		return
	}

	if err := h.loginMgr.QuickLogin(req.TargetURL, req.Email); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Quick login completed",
	})
}

// GetCurrentURL returns current browser URL
func (h *Handlers) GetCurrentURL(c *gin.Context) {
	url, err := h.loginMgr.GetCurrentURL()
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data: map[string]string{
			"url": url,
		},
	})
}

// TakeScreenshot takes a browser screenshot
func (h *Handlers) TakeScreenshot(c *gin.Context) {
	filename := "screenshot_" + utils.GetTimestamp() + ".png"
	
	if err := h.loginMgr.TakeScreenshot(filename); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data: map[string]string{
			"filename": filename,
		},
	})
}

// GetSystemInfo returns system information
func (h *Handlers) GetSystemInfo(c *gin.Context) {
	osName, osVersion, _ := utils.GetOSInfo()
	
	info := map[string]interface{}{
		"os":           osName,
		"os_version":   osVersion,
		"arch":         utils.GetArch(),
		"go_version":   runtime.Version(),
		"is_admin":     utils.IsAdmin(),
		"num_cpu":      runtime.NumCPU(),
		"num_goroutine": runtime.NumGoroutine(),
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    info,
	})
}

// HealthCheck returns health status
func (h *Handlers) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Success: true,
		Data: map[string]interface{}{
			"status":    "healthy",
			"timestamp": time.Now().Unix(),
			"version":   "1.0.0",
		},
	})
}

// WebSocketHandler handles WebSocket connections
func (h *Handlers) WebSocketHandler(c *gin.Context) {
	conn, err := h.wsUpgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Errorf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	h.logger.Info("WebSocket connection established")

	// Handle WebSocket communication
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			h.logger.Errorf("WebSocket read error: %v", err)
			break
		}

		h.logger.Infof("Received WebSocket message: %s", message)

		// Echo the message back (placeholder implementation)
		if err := conn.WriteMessage(messageType, message); err != nil {
			h.logger.Errorf("WebSocket write error: %v", err)
			break
		}
	}

	h.logger.Info("WebSocket connection closed")
}
