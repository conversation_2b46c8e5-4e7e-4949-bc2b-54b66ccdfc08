package browser

import (
	"context"
	"fmt"
	"os"
	"time"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"

	"github.com/chromedp/chromedp"
)

// Chrome represents a Chrome browser instance
type Chrome struct {
	config  *config.Config
	logger  *logger.Logger
	ctx     context.Context
	cancel  context.CancelFunc
	options []chromedp.ExecAllocatorOption
}

// NewChrome creates a new Chrome browser instance
func NewChrome(cfg *config.Config) *Chrome {
	return &Chrome{
		config: cfg,
		logger: logger.Get(),
	}
}

// Start starts the Chrome browser with the specified options
func (c *Chrome) Start() error {
	c.logger.Info(utils.FormatInfo("Starting Chrome browser..."))

	// Prepare Chrome options
	c.prepareOptions()

	// Create allocator context
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), c.options...)
	c.cancel = cancel

	// Create browser context
	c.ctx, _ = chromedp.NewContext(allocCtx)

	// Test the browser by navigating to a simple page
	if err := c.testBrowser(); err != nil {
		c.Stop()
		return fmt.Errorf("browser test failed: %w", err)
	}

	c.logger.Info(utils.FormatSuccess("Chrome browser started successfully"))
	return nil
}

// Stop stops the Chrome browser
func (c *Chrome) Stop() {
	if c.cancel != nil {
		c.cancel()
		c.logger.Info(utils.FormatInfo("Chrome browser stopped"))
	}
}

// Navigate navigates to the specified URL
func (c *Chrome) Navigate(url string) error {
	c.logger.Infof("Navigating to: %s", url)

	ctx, cancel := context.WithTimeout(c.ctx, time.Duration(c.config.Browser.Timeout)*time.Second)
	defer cancel()

	if err := chromedp.Run(ctx, chromedp.Navigate(url)); err != nil {
		return fmt.Errorf("failed to navigate to %s: %w", url, err)
	}

	c.logger.Info(utils.FormatSuccess("Navigation completed"))
	return nil
}

// WaitForElement waits for an element to be present
func (c *Chrome) WaitForElement(selector string, timeout time.Duration) error {
	c.logger.Infof("Waiting for element: %s", selector)

	ctx, cancel := context.WithTimeout(c.ctx, timeout)
	defer cancel()

	if err := chromedp.Run(ctx, chromedp.WaitVisible(selector)); err != nil {
		return fmt.Errorf("element not found: %s: %w", selector, err)
	}

	c.logger.Infof("Element found: %s", selector)
	return nil
}

// ClickElement clicks on an element
func (c *Chrome) ClickElement(selector string) error {
	c.logger.Infof("Clicking element: %s", selector)

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	if err := chromedp.Run(ctx, chromedp.Click(selector)); err != nil {
		return fmt.Errorf("failed to click element %s: %w", selector, err)
	}

	c.logger.Infof("Clicked element: %s", selector)
	return nil
}

// InputText inputs text into an element
func (c *Chrome) InputText(selector, text string) error {
	c.logger.Infof("Inputting text into element: %s", selector)

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	actions := []chromedp.Action{
		chromedp.WaitVisible(selector),
		chromedp.Clear(selector),
		chromedp.SendKeys(selector, text),
	}

	if err := chromedp.Run(ctx, actions...); err != nil {
		return fmt.Errorf("failed to input text into %s: %w", selector, err)
	}

	c.logger.Infof("Text input completed for element: %s", selector)
	return nil
}

// GetText gets text content from an element
func (c *Chrome) GetText(selector string) (string, error) {
	c.logger.Infof("Getting text from element: %s", selector)

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	var text string
	if err := chromedp.Run(ctx, chromedp.Text(selector, &text)); err != nil {
		return "", fmt.Errorf("failed to get text from %s: %w", selector, err)
	}

	c.logger.Infof("Got text from element %s: %s", selector, text)
	return text, nil
}

// GetAttribute gets an attribute value from an element
func (c *Chrome) GetAttribute(selector, attribute string) (string, error) {
	c.logger.Infof("Getting attribute %s from element: %s", attribute, selector)

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	var value string
	if err := chromedp.Run(ctx, chromedp.AttributeValue(selector, attribute, &value, nil)); err != nil {
		return "", fmt.Errorf("failed to get attribute %s from %s: %w", attribute, selector, err)
	}

	c.logger.Infof("Got attribute %s from element %s: %s", attribute, selector, value)
	return value, nil
}

// ExecuteScript executes JavaScript code
func (c *Chrome) ExecuteScript(script string) (interface{}, error) {
	c.logger.Infof("Executing JavaScript: %s", script)

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	var result interface{}
	if err := chromedp.Run(ctx, chromedp.Evaluate(script, &result)); err != nil {
		return nil, fmt.Errorf("failed to execute script: %w", err)
	}

	c.logger.Info("JavaScript execution completed")
	return result, nil
}

// TakeScreenshot takes a screenshot of the current page
func (c *Chrome) TakeScreenshot(filename string) error {
	c.logger.Infof("Taking screenshot: %s", filename)

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	var buf []byte
	if err := chromedp.Run(ctx, chromedp.CaptureScreenshot(&buf)); err != nil {
		return fmt.Errorf("failed to take screenshot: %w", err)
	}

	if err := os.WriteFile(filename, buf, 0644); err != nil {
		return fmt.Errorf("failed to save screenshot: %w", err)
	}

	c.logger.Infof("Screenshot saved: %s", filename)
	return nil
}

// WaitForNavigation waits for page navigation to complete
func (c *Chrome) WaitForNavigation(timeout time.Duration) error {
	c.logger.Info("Waiting for navigation to complete...")

	ctx, cancel := context.WithTimeout(c.ctx, timeout)
	defer cancel()

	if err := chromedp.Run(ctx, chromedp.WaitReady("body")); err != nil {
		return fmt.Errorf("navigation timeout: %w", err)
	}

	c.logger.Info("Navigation completed")
	return nil
}

// GetCurrentURL gets the current page URL
func (c *Chrome) GetCurrentURL() (string, error) {
	var url string
	ctx, cancel := context.WithTimeout(c.ctx, 5*time.Second)
	defer cancel()

	if err := chromedp.Run(ctx, chromedp.Location(&url)); err != nil {
		return "", fmt.Errorf("failed to get current URL: %w", err)
	}

	return url, nil
}

// prepareOptions prepares Chrome launch options
func (c *Chrome) prepareOptions() {
	c.options = []chromedp.ExecAllocatorOption{
		chromedp.NoFirstRun,
		chromedp.NoDefaultBrowserCheck,
		chromedp.DisableGPU,
		chromedp.NoSandbox,
		chromedp.DisableDevShmUsage,
		chromedp.UserAgent(c.config.Browser.UserAgent),
	}

	// Add Chrome executable path if specified
	if c.config.Browser.ChromePath != "" && utils.FileExists(c.config.Browser.ChromePath) {
		c.options = append(c.options, chromedp.ExecPath(c.config.Browser.ChromePath))
		c.logger.Infof("Using Chrome executable: %s", c.config.Browser.ChromePath)
	}

	// Add headless mode if configured
	if c.config.Browser.Headless {
		c.options = append(c.options, chromedp.Headless)
		c.logger.Info("Running in headless mode")
	} else {
		c.logger.Info("Running in windowed mode")
	}

	// Add incognito mode for privacy
	c.options = append(c.options, chromedp.Flag("incognito", true))

	// Add window size
	c.options = append(c.options, chromedp.WindowSize(1280, 720))

	// Disable images for faster loading (optional)
	c.options = append(c.options, chromedp.Flag("disable-images", false))

	// Add user data directory for session persistence
	c.options = append(c.options, chromedp.UserDataDir(""))
}

// testBrowser tests if the browser is working correctly
func (c *Chrome) testBrowser() error {
	c.logger.Info("Testing browser functionality...")

	ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()

	// Navigate to a simple test page
	if err := chromedp.Run(ctx, chromedp.Navigate("data:text/html,<html><body><h1>Browser Test</h1></body></html>")); err != nil {
		return fmt.Errorf("browser test navigation failed: %w", err)
	}

	// Check if we can find the test element
	var text string
	if err := chromedp.Run(ctx, chromedp.Text("h1", &text)); err != nil {
		return fmt.Errorf("browser test element check failed: %w", err)
	}

	if text != "Browser Test" {
		return fmt.Errorf("browser test content mismatch: expected 'Browser Test', got '%s'", text)
	}

	c.logger.Info("Browser test passed")
	return nil
}

// AddRandomDelay adds a random delay to simulate human behavior
func (c *Chrome) AddRandomDelay(minMs, maxMs int) {
	delay := utils.GenerateRandomDelay(minMs, maxMs)
	c.logger.Infof("Adding random delay: %v", delay)
	time.Sleep(delay)
}
