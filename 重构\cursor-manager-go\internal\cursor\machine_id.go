package cursor

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"runtime"

	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"

	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
)

// MachineIDResetter handles Cursor machine ID reset operations
type MachineIDResetter struct {
	paths  *Paths
	logger *logger.Logger
}

// MachineIDs represents the set of machine IDs to be generated
type MachineIDs struct {
	DeviceID         string `json:"dev_device_id"`
	VSCodeMachineID  string `json:"vscode_machine_id"`
	VSCodeTelemetryID string `json:"vscode_telemetry_id"`
	TelemetryDeviceID string `json:"telemetry_device_id"`
}

// NewMachineIDResetter creates a new MachineIDResetter
func NewMachineIDResetter() (*MachineIDResetter, error) {
	paths, err := GetCursorPaths()
	if err != nil {
		return nil, fmt.Errorf("failed to get Cursor paths: %w", err)
	}

	return &MachineIDResetter{
		paths:  paths,
		logger: logger.Get(),
	}, nil
}

// Reset performs the complete machine ID reset process
func (r *MachineIDResetter) Reset() error {
	r.logger.Info(utils.FormatStep(1, "Starting Cursor machine ID reset"))
	r.logger.Infof("Operating System: %s", runtime.GOOS)

	// Step 1: Generate new IDs
	r.logger.Info(utils.FormatStep(2, "Generating new machine IDs"))
	newIDs, err := r.generateNewIDs()
	if err != nil {
		return fmt.Errorf("failed to generate new IDs: %w", err)
	}

	// Step 2: Update SQLite database
	r.logger.Info(utils.FormatStep(3, "Updating SQLite database"))
	if err := r.updateSQLiteDB(newIDs); err != nil {
		r.logger.Warnf("Failed to update SQLite database: %v", err)
		// Continue with other steps even if database update fails
	}

	// Step 3: Update system-specific IDs
	r.logger.Info(utils.FormatStep(4, "Updating system-specific IDs"))
	if err := r.updateSystemSpecificIDs(); err != nil {
		r.logger.Warnf("Failed to update system-specific IDs: %v", err)
		// Continue with other steps
	}

	// Step 4: Update machine ID file
	r.logger.Info(utils.FormatStep(5, "Updating machine ID file"))
	if err := r.updateMachineIDFile(newIDs.DeviceID); err != nil {
		return fmt.Errorf("failed to update machine ID file: %w", err)
	}

	r.logger.Info(utils.FormatSuccess("Machine ID reset completed successfully!"))
	r.logger.Info(utils.FormatWarning("Important: Please restart Cursor for all changes to take effect"))

	return nil
}

// generateNewIDs generates a new set of machine IDs
func (r *MachineIDResetter) generateNewIDs() (*MachineIDs, error) {
	r.logger.Info(utils.FormatInfo("Generating new machine IDs..."))

	ids := &MachineIDs{
		DeviceID:          uuid.New().String(),
		VSCodeMachineID:   fmt.Sprintf("vscode-machine-id-%s", uuid.New().String()),
		VSCodeTelemetryID: fmt.Sprintf("vscode-telemetry-machine-id-%s", uuid.New().String()),
		TelemetryDeviceID: uuid.New().String(),
	}

	// Remove dashes from telemetry device ID (typically a hex string without dashes)
	ids.TelemetryDeviceID = fmt.Sprintf("%s", uuid.New().String())
	ids.TelemetryDeviceID = ids.TelemetryDeviceID[:32] // Take first 32 characters

	// Log generated IDs
	r.logger.Info(utils.FormatInfo("Generated machine ID details:"))
	r.logger.Infof("   %s Device ID: %s", utils.GetEmoji("key"), ids.DeviceID)
	r.logger.Infof("   %s VSCode Machine ID: %s", utils.GetEmoji("key"), ids.VSCodeMachineID)
	r.logger.Infof("   %s VSCode Telemetry ID: %s", utils.GetEmoji("key"), ids.VSCodeTelemetryID)
	r.logger.Infof("   %s Telemetry Device ID: %s", utils.GetEmoji("key"), ids.TelemetryDeviceID)

	r.logger.Info(utils.FormatSuccess("New machine IDs generated successfully"))
	return ids, nil
}

// updateSQLiteDB updates the SQLite database with new IDs
func (r *MachineIDResetter) updateSQLiteDB(ids *MachineIDs) error {
	if !utils.FileExists(r.paths.DBPath) {
		r.logger.Warn(utils.FormatWarning("SQLite database not found, skipping update: " + r.paths.DBPath))
		return nil
	}

	r.logger.Infof("%s Updating SQLite database: %s", utils.GetEmoji("database"), r.paths.DBPath)

	// Define items to update
	itemsToUpdate := map[string]string{
		"telemetry.machineId": fmt.Sprintf(`{"id": "%s"}`, ids.VSCodeTelemetryID),
		"storage.machineId":   ids.VSCodeMachineID,
		"telemetry.deviceId":  ids.TelemetryDeviceID,
	}

	r.logger.Info(utils.FormatInfo("Will update the following database keys:"))
	for key, value := range itemsToUpdate {
		displayValue := value
		if len(displayValue) > 50 {
			displayValue = displayValue[:50] + "..."
		}
		r.logger.Infof("   %s %s = %s", utils.GetEmoji("key"), key, displayValue)
	}

	// Open database
	db, err := sql.Open("sqlite3", r.paths.DBPath)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}
	defer db.Close()

	// Begin transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	updatedCount := 0
	insertedCount := 0

	for key, value := range itemsToUpdate {
		// Try to update existing record
		result, err := tx.Exec("UPDATE ItemTable SET value = ? WHERE key = ?", value, key)
		if err != nil {
			return fmt.Errorf("failed to update key %s: %w", key, err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected for key %s: %w", key, err)
		}

		if rowsAffected == 0 {
			// Key doesn't exist, insert it
			r.logger.Infof("   %s Key '%s' doesn't exist, inserting...", utils.GetEmoji("info"), key)
			_, err := tx.Exec("INSERT INTO ItemTable (key, value) VALUES (?, ?)", key, value)
			if err != nil {
				return fmt.Errorf("failed to insert key %s: %w", key, err)
			}
			insertedCount++
		} else {
			r.logger.Infof("   %s Key '%s' updated", utils.GetEmoji("success"), key)
			updatedCount++
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	r.logger.Info(utils.FormatSuccess("SQLite database update completed"))
	r.logger.Infof("   %s Updated %d keys, inserted %d keys", utils.GetEmoji("chart"), updatedCount, insertedCount)

	return nil
}

// updateSystemSpecificIDs updates system-specific IDs
func (r *MachineIDResetter) updateSystemSpecificIDs() error {
	switch runtime.GOOS {
	case "windows":
		return r.updateWindowsMachineGUID()
	case "darwin":
		return r.updateMacOSPlatformUUID()
	case "linux":
		r.logger.Info(utils.FormatInfo("Linux system detected - no system-specific ID updates required"))
		return nil
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// updateWindowsMachineGUID updates Windows registry MachineGuid
func (r *MachineIDResetter) updateWindowsMachineGUID() error {
	r.logger.Infof("%s Updating Windows registry MachineGuid...", utils.GetEmoji("window"))

	// Check if running as administrator
	if !utils.IsAdmin() {
		r.logger.Error(utils.FormatError("Administrator privileges required to update Windows registry"))
		r.logger.Info(utils.FormatInfo("Please run the application as administrator"))
		return fmt.Errorf("insufficient privileges")
	}

	newGUID := uuid.New().String()
	r.logger.Infof("%s Registry path: HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography", utils.GetEmoji("registry"))
	r.logger.Infof("%s New MachineGuid: %s", utils.GetEmoji("key"), newGUID)

	// Note: In a real implementation, you would use golang.org/x/sys/windows/registry
	// For now, we'll use a system command as a placeholder
	r.logger.Warn(utils.FormatWarning("Registry update not implemented in this version"))
	r.logger.Info(utils.FormatInfo("Please manually update the registry or run with appropriate privileges"))

	return nil
}

// updateMacOSPlatformUUID handles macOS platform UUID
func (r *MachineIDResetter) updateMacOSPlatformUUID() error {
	r.logger.Info(utils.FormatInfo("Checking macOS platform UUID..."))
	r.logger.Warn(utils.FormatWarning("Note: Directly changing macOS platform UUID is complex and risky"))
	r.logger.Info(utils.FormatInfo("The script will not perform this operation for safety reasons"))
	return nil
}

// updateMachineIDFile updates the machine ID file
func (r *MachineIDResetter) updateMachineIDFile(machineID string) error {
	r.logger.Infof("%s Updating machine ID file...", utils.GetEmoji("file"))
	r.logger.Infof("%s File path: %s", utils.GetEmoji("folder"), r.paths.MachineIDPath)
	r.logger.Infof("%s New machine ID: %s", utils.GetEmoji("key"), machineID)

	// Ensure directory exists
	if err := r.paths.EnsureDirectories(); err != nil {
		return fmt.Errorf("failed to create directories: %w", err)
	}

	// Write machine ID to file
	if err := os.WriteFile(r.paths.MachineIDPath, []byte(machineID), 0644); err != nil {
		return fmt.Errorf("failed to write machine ID file: %w", err)
	}

	// Verify the write
	fileInfo, err := os.Stat(r.paths.MachineIDPath)
	if err != nil {
		return fmt.Errorf("failed to verify machine ID file: %w", err)
	}

	r.logger.Info(utils.FormatSuccess("Machine ID file updated successfully"))
	r.logger.Infof("%s File size: %d bytes", utils.GetEmoji("chart"), fileInfo.Size())

	return nil
}
