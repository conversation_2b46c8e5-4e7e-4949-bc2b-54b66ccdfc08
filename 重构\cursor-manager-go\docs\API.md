# Cursor Manager Go - API Documentation

This document describes the REST API endpoints provided by Cursor Manager Go.

## Base URL

All API endpoints are prefixed with `/api/v1`.

## Authentication

Currently, no authentication is required as this is a local application.

## Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {},
  "error": "",
  "message": ""
}
```

- `success`: <PERSON><PERSON><PERSON> indicating if the request was successful
- `data`: Response data (only present on success)
- `error`: Error message (only present on failure)
- `message`: Additional message (optional)

## Endpoints

### Configuration

#### GET /api/v1/config

Get current configuration.

**Response:**
```json
{
  "success": true,
  "data": {
    "email_prefix": "test",
    "email_password": "password123",
    "email_domain": "2925.com",
    "browser_path": "/usr/bin/google-chrome",
    "browser_headless": false,
    "server_port": 8080,
    "debug": false
  }
}
```

#### POST /api/v1/config

Save configuration.

**Request Body:**
```json
{
  "email_prefix": "test",
  "email_password": "password123",
  "email_domain": "2925.com",
  "browser_path": "/usr/bin/google-chrome",
  "browser_headless": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Configuration saved successfully"
}
```

### Email Management

#### GET /api/v1/email/generate

Generate a random email address.

**Response:**
```json
{
  "success": true,
  "data": {
    "email": "<EMAIL>"
  }
}
```

#### POST /api/v1/email/test

Test email connection.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email connection test successful"
}
```

#### POST /api/v1/email/monitor/start

Start email monitoring for verification codes.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email monitoring started"
}
```

#### POST /api/v1/email/monitor/stop

Stop email monitoring.

**Response:**
```json
{
  "success": true,
  "message": "Email monitoring stopped"
}
```

#### GET /api/v1/email/monitor/status

Get email monitoring status.

**Response:**
```json
{
  "success": true,
  "data": {
    "is_running": true,
    "check_count": 5,
    "last_check": "2024-01-01T12:00:00Z",
    "uptime": "30s"
  }
}
```

### Cursor Management

#### GET /api/v1/cursor/status

Get Cursor application status.

**Response:**
```json
{
  "success": true,
  "data": {
    "is_running": true,
    "process_info": {
      "Cursor.exe": [1234, 5678]
    },
    "paths": {
      "data_path": "C:\\Users\\<USER>\\AppData\\Roaming\\Cursor",
      "db_path": "C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\state.vscdb",
      "machine_id_path": "C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\machineId",
      "app_path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Cursor\\Cursor.exe"
    },
    "paths_valid": true,
    "database_exists": true,
    "machine_id_exists": true
  }
}
```

#### POST /api/v1/cursor/reset

Reset Cursor environment (machine ID, processes, etc.).

**Response:**
```json
{
  "success": true,
  "message": "Environment reset started"
}
```

#### POST /api/v1/cursor/restart

Restart Cursor application.

**Response:**
```json
{
  "success": true,
  "message": "Cursor restart initiated"
}
```

#### POST /api/v1/cursor/launch

Launch Cursor application.

**Response:**
```json
{
  "success": true,
  "message": "Cursor launch initiated"
}
```

#### POST /api/v1/cursor/terminate

Terminate Cursor processes.

**Response:**
```json
{
  "success": true,
  "message": "Cursor termination initiated"
}
```

### Browser Automation

#### POST /api/v1/browser/login

Perform automated login with email verification.

**Request Body:**
```json
{
  "target_url": "https://example.com/login",
  "login_email": "<EMAIL>",
  "monitoring_email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "message": "Auto-login completed successfully",
    "verification_code": "123456",
    "final_url": "https://example.com/dashboard"
  }
}
```

#### POST /api/v1/browser/quick-login

Perform quick login (browser automation only, no email monitoring).

**Request Body:**
```json
{
  "target_url": "https://example.com/login",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Quick login completed"
}
```

#### GET /api/v1/browser/current-url

Get current browser URL.

**Response:**
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/dashboard"
  }
}
```

#### POST /api/v1/browser/screenshot

Take a screenshot of the current browser state.

**Response:**
```json
{
  "success": true,
  "data": {
    "filename": "screenshot_**********.png"
  }
}
```

### System Information

#### GET /api/v1/system/info

Get system information.

**Response:**
```json
{
  "success": true,
  "data": {
    "os": "windows",
    "os_version": "Microsoft Windows 10",
    "arch": "amd64",
    "go_version": "go1.21.0",
    "is_admin": true,
    "num_cpu": 8,
    "num_goroutine": 15
  }
}
```

#### GET /api/v1/system/health

Health check endpoint.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": **********,
    "version": "1.0.0"
  }
}
```

### WebSocket

#### GET /api/v1/ws

WebSocket endpoint for real-time updates.

**Connection URL:** `ws://localhost:8080/api/v1/ws`

**Message Format:**
```json
{
  "type": "status",
  "level": "info",
  "message": "Operation completed"
}
```

**Message Types:**
- `status`: Status updates with levels (info, success, warning, error)
- `verification_code`: Verification code received
- `progress`: Progress updates for long-running operations

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request format or parameters
- `500 Internal Server Error`: Server error

Error responses include details in the `error` field:

```json
{
  "success": false,
  "error": "Invalid email format"
}
```

## Rate Limiting

Basic rate limiting is implemented to prevent abuse. Requests are limited to 1 per second per IP address.

## CORS

CORS is enabled for all origins to support local development and testing.

## Examples

### Complete Auto-Login Flow

1. **Configure email settings:**
```bash
curl -X POST http://localhost:8080/api/v1/config \
  -H "Content-Type: application/json" \
  -d '{"email_prefix": "test", "email_password": "password123"}'
```

2. **Generate login email:**
```bash
curl http://localhost:8080/api/v1/email/generate
```

3. **Perform auto-login:**
```bash
curl -X POST http://localhost:8080/api/v1/browser/login \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://example.com/login",
    "login_email": "<EMAIL>",
    "monitoring_email": "<EMAIL>",
    "password": "password123"
  }'
```

### Environment Reset

```bash
curl -X POST http://localhost:8080/api/v1/cursor/reset
```

### Email Monitoring

```bash
# Start monitoring
curl -X POST http://localhost:8080/api/v1/email/monitor/start \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# Check status
curl http://localhost:8080/api/v1/email/monitor/status

# Stop monitoring
curl -X POST http://localhost:8080/api/v1/email/monitor/stop
```
