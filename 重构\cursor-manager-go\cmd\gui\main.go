package main

import (
	"fmt"
	"os"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/pkg/logger"
)

var (
	Version   = "1.0.0"
	BuildTime = "unknown"
)

func main() {
	fmt.Printf("Cursor Manager Go GUI v%s\n", Version)
	fmt.Printf("Build Time: %s\n", BuildTime)
	fmt.Println()
	fmt.Println("GUI version is not yet implemented.")
	fmt.Println("Please use the web version instead:")
	fmt.Println()
	fmt.Println("  ./cursor-manager-web")
	fmt.Println()
	fmt.Println("Or use the CLI version:")
	fmt.Println()
	fmt.Println("  ./cursor-manager-cli --help")
	fmt.Println()

	// Load configuration to show current settings
	cfg, err := config.Load("")
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	loggerConfig := &logger.Config{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	}

	if err := logger.Init(loggerConfig); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}

	log := logger.Get()
	log.Info("GUI version is planned for future release")
	log.Infof("Current configuration file: %s", getConfigPath())
	log.Infof("Web server would run on: http://%s:%d", cfg.Server.Host, cfg.Server.Port)

	fmt.Println("Future GUI features will include:")
	fmt.Println("  - Native desktop application")
	fmt.Println("  - System tray integration")
	fmt.Println("  - Desktop notifications")
	fmt.Println("  - File dialogs for configuration")
	fmt.Println("  - Offline operation")
	fmt.Println()
	fmt.Println("For now, please use the web interface which provides")
	fmt.Println("all the same functionality with a modern UI.")
}

func getConfigPath() string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "config.yaml"
	}
	return fmt.Sprintf("%s/.cursor-manager/config.yaml", homeDir)
}
