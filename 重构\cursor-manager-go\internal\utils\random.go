package utils

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/google/uuid"
)

const (
	// Character sets for random generation
	LowercaseLetters = "abcdefghijklmnopqrstuvwxyz"
	UppercaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	Digits           = "0123456789"
	SpecialChars     = "!@#$%^&*()_+-=[]{}|;:,.<>?"
	
	// Default lengths
	DefaultEmailSuffixLength = 7
	DefaultPasswordLength    = 16
	DefaultTokenLength       = 32
)

// GenerateRandomString generates a random string of specified length using the given character set
func GenerateRandomString(length int, charset string) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("length must be positive")
	}
	
	if charset == "" {
		charset = LowercaseLetters + UppercaseLetters + Digits
	}
	
	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))
	
	for i := 0; i < length; i++ {
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", fmt.Errorf("failed to generate random number: %w", err)
		}
		result[i] = charset[randomIndex.Int64()]
	}
	
	return string(result), nil
}

// GenerateRandomEmail generates a random email address with the given prefix and domain
func GenerateRandomEmail(prefix, domain string) (string, error) {
	if prefix == "" {
		return "", fmt.Errorf("prefix cannot be empty")
	}
	
	if domain == "" {
		domain = "2925.com"
	}
	
	suffix, err := GenerateRandomString(DefaultEmailSuffixLength, LowercaseLetters)
	if err != nil {
		return "", fmt.Errorf("failed to generate email suffix: %w", err)
	}
	
	return fmt.Sprintf("%s%s@%s", prefix, suffix, domain), nil
}

// GenerateRandomPassword generates a random password with specified requirements
func GenerateRandomPassword(length int, includeUppercase, includeLowercase, includeDigits, includeSpecial bool) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("length must be positive")
	}
	
	var charset string
	var required []string
	
	if includeUppercase {
		charset += UppercaseLetters
		required = append(required, UppercaseLetters)
	}
	
	if includeLowercase {
		charset += LowercaseLetters
		required = append(required, LowercaseLetters)
	}
	
	if includeDigits {
		charset += Digits
		required = append(required, Digits)
	}
	
	if includeSpecial {
		charset += SpecialChars
		required = append(required, SpecialChars)
	}
	
	if charset == "" {
		return "", fmt.Errorf("at least one character type must be included")
	}
	
	// Generate password ensuring at least one character from each required set
	password := make([]byte, length)
	
	// First, add one character from each required set
	for i, requiredSet := range required {
		if i >= length {
			break
		}
		
		charIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(requiredSet))))
		if err != nil {
			return "", fmt.Errorf("failed to generate random character: %w", err)
		}
		password[i] = requiredSet[charIndex.Int64()]
	}
	
	// Fill the rest with random characters from the full charset
	for i := len(required); i < length; i++ {
		charIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", fmt.Errorf("failed to generate random character: %w", err)
		}
		password[i] = charset[charIndex.Int64()]
	}
	
	// Shuffle the password to avoid predictable patterns
	if err := shuffleBytes(password); err != nil {
		return "", fmt.Errorf("failed to shuffle password: %w", err)
	}
	
	return string(password), nil
}

// GenerateUUID generates a new UUID
func GenerateUUID() string {
	return uuid.New().String()
}

// GenerateShortID generates a short random ID (8 characters)
func GenerateShortID() (string, error) {
	return GenerateRandomString(8, LowercaseLetters+Digits)
}

// GenerateToken generates a random token for authentication
func GenerateToken(length int) (string, error) {
	if length <= 0 {
		length = DefaultTokenLength
	}
	
	return GenerateRandomString(length, LowercaseLetters+UppercaseLetters+Digits)
}

// GenerateNumericCode generates a random numeric code (like verification codes)
func GenerateNumericCode(length int) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("length must be positive")
	}
	
	return GenerateRandomString(length, Digits)
}

// GenerateRandomDelay generates a random delay between min and max milliseconds
func GenerateRandomDelay(minMs, maxMs int) time.Duration {
	if minMs >= maxMs {
		return time.Duration(minMs) * time.Millisecond
	}
	
	diff := maxMs - minMs
	randomMs, err := rand.Int(rand.Reader, big.NewInt(int64(diff)))
	if err != nil {
		return time.Duration(minMs) * time.Millisecond
	}
	
	return time.Duration(minMs+int(randomMs.Int64())) * time.Millisecond
}

// GenerateRandomUserAgent generates a random user agent string
func GenerateRandomUserAgent() string {
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
	}
	
	index, err := rand.Int(rand.Reader, big.NewInt(int64(len(userAgents))))
	if err != nil {
		return userAgents[0] // Fallback to first user agent
	}
	
	return userAgents[index.Int64()]
}

// shuffleBytes shuffles a byte slice in place using Fisher-Yates algorithm
func shuffleBytes(slice []byte) error {
	for i := len(slice) - 1; i > 0; i-- {
		j, err := rand.Int(rand.Reader, big.NewInt(int64(i+1)))
		if err != nil {
			return err
		}
		slice[i], slice[j.Int64()] = slice[j.Int64()], slice[i]
	}
	return nil
}

// IsValidEmail performs basic email validation
func IsValidEmail(email string) bool {
	if email == "" {
		return false
	}
	
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}
	
	local, domain := parts[0], parts[1]
	
	// Basic validation
	if len(local) == 0 || len(domain) == 0 {
		return false
	}
	
	if !strings.Contains(domain, ".") {
		return false
	}
	
	return true
}

// SanitizeString removes potentially dangerous characters from a string
func SanitizeString(input string) string {
	// Remove control characters and other potentially dangerous characters
	var result strings.Builder
	for _, r := range input {
		if r >= 32 && r <= 126 { // Printable ASCII characters
			result.WriteRune(r)
		}
	}
	return result.String()
}
