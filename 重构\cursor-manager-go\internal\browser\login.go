package browser

import (
	"context"
	"fmt"
	"time"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/email"
	"cursor-manager-go/internal/utils"
	"cursor-manager-go/pkg/logger"
)

// LoginManager manages the complete login flow
type LoginManager struct {
	automation    *Automation
	emailMonitor  *email.Monitor
	emailGen      *email.Generator
	config        *config.Config
	logger        *logger.Logger
}

// LoginRequest represents a login request
type LoginRequest struct {
	TargetURL       string `json:"target_url"`
	LoginEmail      string `json:"login_email"`
	MonitoringEmail string `json:"monitoring_email"`
	Password        string `json:"password"`
}

// LoginResult represents the result of a login attempt
type LoginResult struct {
	Success         bool   `json:"success"`
	Message         string `json:"message"`
	VerificationCode string `json:"verification_code,omitempty"`
	FinalURL        string `json:"final_url,omitempty"`
	Error           string `json:"error,omitempty"`
}

// NewLoginManager creates a new login manager
func NewLoginManager(cfg *config.Config) *LoginManager {
	return &LoginManager{
		automation:   NewAutomation(cfg),
		emailMonitor: email.NewMonitor(cfg),
		emailGen:     email.NewGenerator(cfg),
		config:       cfg,
		logger:       logger.Get(),
	}
}

// PerformAutoLogin performs the complete automated login flow
func (lm *LoginManager) PerformAutoLogin(ctx context.Context, request *LoginRequest) (*LoginResult, error) {
	lm.logger.Info(utils.FormatInfo("Starting complete auto-login flow"))

	result := &LoginResult{}

	// Validate request
	if err := lm.validateRequest(request); err != nil {
		result.Success = false
		result.Error = err.Error()
		return result, err
	}

	// Step 1: Start email monitoring
	lm.logger.Info(utils.FormatStep(1, "Starting email monitoring"))
	if err := lm.startEmailMonitoring(ctx, request); err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Email monitoring failed: %v", err)
		return result, err
	}
	defer lm.emailMonitor.Stop()

	// Step 2: Start browser automation
	lm.logger.Info(utils.FormatStep(2, "Starting browser automation"))
	if err := lm.automation.Start(); err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Browser automation failed: %v", err)
		return result, err
	}
	defer lm.automation.Stop()

	// Step 3: Perform automated login
	lm.logger.Info(utils.FormatStep(3, "Performing automated login"))
	codeChannel := lm.emailMonitor.GetCodeChannel()
	
	if err := lm.automation.AutoLogin(ctx, request.TargetURL, request.LoginEmail, codeChannel); err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Auto-login failed: %v", err)
		return result, err
	}

	// Step 4: Get final URL
	finalURL, err := lm.automation.GetCurrentURL()
	if err != nil {
		lm.logger.Warnf("Failed to get final URL: %v", err)
	} else {
		result.FinalURL = finalURL
	}

	result.Success = true
	result.Message = "Auto-login completed successfully"
	
	lm.logger.Info(utils.FormatSuccess("Complete auto-login flow finished"))
	return result, nil
}

// GenerateRandomEmail generates a random email for login
func (lm *LoginManager) GenerateRandomEmail() (string, error) {
	return lm.emailGen.GenerateRandom()
}

// GetMonitoringEmail returns the monitoring email address
func (lm *LoginManager) GetMonitoringEmail() string {
	return lm.emailGen.GenerateMonitoringEmail()
}

// TestEmailConnection tests the email connection
func (lm *LoginManager) TestEmailConnection(email, password string) error {
	client := email.NewPOP3Client(email, password)
	return client.TestConnection()
}

// StartEmailMonitoringOnly starts only email monitoring (for manual login)
func (lm *LoginManager) StartEmailMonitoringOnly(ctx context.Context, email, password string) error {
	lm.logger.Info(utils.FormatInfo("Starting email monitoring only"))

	monitorConfig := email.DefaultMonitorConfig(email, password)
	
	if err := lm.emailMonitor.Start(ctx, monitorConfig); err != nil {
		return fmt.Errorf("failed to start email monitoring: %w", err)
	}

	lm.logger.Info(utils.FormatSuccess("Email monitoring started"))
	return nil
}

// GetEmailMonitorStatus returns the status of email monitoring
func (lm *LoginManager) GetEmailMonitorStatus() map[string]interface{} {
	return lm.emailMonitor.GetStats()
}

// StopEmailMonitoring stops email monitoring
func (lm *LoginManager) StopEmailMonitoring() {
	lm.emailMonitor.Stop()
}

// GetStatusChannel returns the email monitor status channel
func (lm *LoginManager) GetStatusChannel() <-chan string {
	return lm.emailMonitor.GetStatusChannel()
}

// GetCodeChannel returns the verification code channel
func (lm *LoginManager) GetCodeChannel() <-chan string {
	return lm.emailMonitor.GetCodeChannel()
}

// validateRequest validates the login request
func (lm *LoginManager) validateRequest(request *LoginRequest) error {
	if request.TargetURL == "" {
		return fmt.Errorf("target URL is required")
	}

	if request.LoginEmail == "" {
		return fmt.Errorf("login email is required")
	}

	if request.MonitoringEmail == "" {
		return fmt.Errorf("monitoring email is required")
	}

	if request.Password == "" {
		return fmt.Errorf("email password is required")
	}

	// Validate email formats
	if err := lm.emailGen.ValidateEmail(request.LoginEmail); err != nil {
		return fmt.Errorf("invalid login email: %w", err)
	}

	if err := lm.emailGen.ValidateEmail(request.MonitoringEmail); err != nil {
		return fmt.Errorf("invalid monitoring email: %w", err)
	}

	return nil
}

// startEmailMonitoring starts the email monitoring process
func (lm *LoginManager) startEmailMonitoring(ctx context.Context, request *LoginRequest) error {
	monitorConfig := &email.MonitorConfig{
		Email:           request.MonitoringEmail,
		Password:        request.Password,
		CheckInterval:   5 * time.Second,
		Timeout:         time.Duration(lm.config.Email.Timeout) * time.Second,
		MaxChecks:       18, // 90 seconds / 5 seconds
		DeleteAfterRead: false,
	}

	return lm.emailMonitor.Start(ctx, monitorConfig)
}

// QuickLogin performs a quick login without full automation (for testing)
func (lm *LoginManager) QuickLogin(targetURL, email string) error {
	lm.logger.Info(utils.FormatInfo("Starting quick login (browser only)"))

	if err := lm.automation.Start(); err != nil {
		return fmt.Errorf("failed to start browser: %w", err)
	}
	defer lm.automation.Stop()

	// Just navigate and fill email
	if err := lm.automation.chrome.Navigate(targetURL); err != nil {
		return fmt.Errorf("navigation failed: %w", err)
	}

	emailSelector := `input[name="email"]`
	if err := lm.automation.chrome.WaitForElement(emailSelector, 10*time.Second); err != nil {
		return fmt.Errorf("email field not found: %w", err)
	}

	if err := lm.automation.chrome.InputText(emailSelector, email); err != nil {
		return fmt.Errorf("failed to fill email: %w", err)
	}

	lm.logger.Info(utils.FormatSuccess("Quick login completed - please continue manually"))
	return nil
}

// TakeScreenshot takes a screenshot of the current browser state
func (lm *LoginManager) TakeScreenshot(filename string) error {
	return lm.automation.TakeScreenshot(filename)
}

// GetCurrentURL returns the current browser URL
func (lm *LoginManager) GetCurrentURL() (string, error) {
	return lm.automation.GetCurrentURL()
}

// IsEmailMonitoringRunning checks if email monitoring is active
func (lm *LoginManager) IsEmailMonitoringRunning() bool {
	return lm.emailMonitor.IsRunning()
}

// WaitForVerificationCode waits for a verification code with timeout
func (lm *LoginManager) WaitForVerificationCode(timeout time.Duration) (string, error) {
	return lm.emailMonitor.WaitForCode(timeout)
}
