"""
续杯工具 - 纯本地版本构建脚本
使用PyInstaller将纯本地界面版本打包成可执行文件
"""

import os
import shutil
import PyInstaller.__main__
import platform

# --- 配置 ---
SCRIPT_NAME = '启动本地界面.py'
EXE_NAME = '续杯工具-纯本地版'
DRIVERS_DIR = 'drivers'
LOCAL_UI_DIR = '本地界面'
DIST_DIR = './dist'
BUILD_DIR = './build'

def build_local_version():
    """构建纯本地版本"""
    print("--- 🚀 开始构建纯本地界面版本 ---")

    pyinstaller_options = [
        '--name', EXE_NAME,
        '--onefile',
        '--windowed',
        '--clean',
        '--noconfirm',
        f'--distpath', DIST_DIR,
        f'--workpath', BUILD_DIR,
    ]

    # 添加数据文件
    data_dirs = [
        (DRIVERS_DIR, DRIVERS_DIR),
        (LOCAL_UI_DIR, LOCAL_UI_DIR)
    ]

    for src_dir, dest_dir in data_dirs:
        abs_src_dir = os.path.abspath(src_dir)
        if os.path.isdir(abs_src_dir):
            print(f"📦 添加数据目录: '{abs_src_dir}' -> '{dest_dir}'")
            pyinstaller_options.extend(['--add-data', f'{abs_src_dir}{os.pathsep}{dest_dir}'])
        else:
            print(f"⚠️ 警告: 未找到目录 '{abs_src_dir}'")
            print(f"   当前工作目录: {os.getcwd()}")
            print(f"   目录内容: {os.listdir('.')}")

    # 添加隐藏导入
    hidden_imports = [
        'multiprocessing',
        'sqlite3',
        'pywinauto',
        'configparser',
        'DrissionPage',
        'colorama',
        'pywebview',
        'pywebview.platforms.winforms',  # Windows平台
        'pywebview.platforms.cef',       # CEF后端
        'pywebview.platforms.qt',        # Qt后端
        # 添加标准库模块
        'poplib',
        'smtplib',
        'email',
        'email.mime',
        'email.mime.text',
        'email.mime.multipart',
        'imaplib',
        # 添加项目模块
        '配置管理',
        '应用管理',
        '网页操作',
        '工具模块',
        '邮箱接收',
        '界面组件',
        '本地界面.本地界面管理器'
    ]
    
    for lib in hidden_imports:
        print(f"➕ 添加隐藏导入: '{lib}'")
        pyinstaller_options.extend(['--hidden-import', lib])

    command = [
        SCRIPT_NAME,
        *pyinstaller_options
    ]

    print("\n--- 运行PyInstaller ---")
    print(f"命令: pyinstaller {' '.join(command)}")

    try:
        PyInstaller.__main__.run(command)
        print("\n--- ✅ PyInstaller构建成功 ---")

        exe_filename = f'{EXE_NAME}.exe' if platform.system() == 'Windows' else EXE_NAME
        src_exe_path = os.path.join(DIST_DIR, exe_filename)
        dest_exe_path = f'./{exe_filename}'

        if os.path.exists(src_exe_path):
            print(f"📦 移动 '{src_exe_path}' 到项目根目录 -> '{dest_exe_path}'")
            if os.path.exists(dest_exe_path):
                os.remove(dest_exe_path)
            shutil.move(src_exe_path, './')
            print(f"✅ 可执行文件已准备就绪: {os.path.abspath(dest_exe_path)}")
        else:
            print(f"❌ 错误: 在 '{src_exe_path}' 找不到构建的可执行文件")

    except Exception as e:
        print(f"\n--- ❌ 构建过程中发生错误 ---")
        print(str(e))

    finally:
        print("\n--- 🧹 清理构建产物 ---")
        if os.path.isdir(BUILD_DIR):
            shutil.rmtree(BUILD_DIR)
        if os.path.isdir(DIST_DIR):
            shutil.rmtree(DIST_DIR)
        spec_file = f'{EXE_NAME}.spec'
        if os.path.exists(spec_file):
            os.remove(spec_file)
        print("--- ✨ 构建过程完成 ---")

def main():
    """主函数"""
    # 自动切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    original_dir = os.getcwd()

    print(f"📁 脚本位置: {script_dir}")
    print(f"📁 当前目录: {original_dir}")

    # 切换到脚本目录
    os.chdir(script_dir)
    print(f"📁 切换到脚本目录: {os.getcwd()}")

    try:
        if not os.path.exists(SCRIPT_NAME):
            print(f"❌ 错误: 主脚本 '{SCRIPT_NAME}' 不存在。")
            print("请确保脚本在正确的项目目录中。")
            input("按回车键退出...")
            return

        if not os.path.exists(LOCAL_UI_DIR):
            print(f"❌ 错误: 本地界面目录 '{LOCAL_UI_DIR}' 不存在。")
            print("请确保本地界面文件存在。")
            input("按回车键退出...")
            return
    
        print("🔧 开始构建续杯工具 - 纯本地版本")
        print("📋 这个版本不需要服务器，完全本地运行")

        try:
            build_local_version()
            print("\n🎉 构建完成！")
            print("💡 现在您可以运行生成的可执行文件")
        except Exception as e:
            print(f"\n❌ 构建失败: {e}")
            import traceback
            traceback.print_exc()

        input("\n按回车键退出...")

    finally:
        # 恢复原始目录
        os.chdir(original_dir)

if __name__ == '__main__':
    main()
