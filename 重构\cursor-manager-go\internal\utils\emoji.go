package utils

// Emoji constants for consistent UI feedback
const (
	// Status emojis
	EmojiSuccess = "✅"
	EmojiError   = "❌"
	EmojiWarning = "⚠️"
	EmojiInfo    = "ℹ️"
	EmojiLoading = "⏳"
	
	// Action emojis
	EmojiRocket    = "🚀"
	EmojiGear      = "⚙️"
	EmojiRefresh   = "🔄"
	EmojiEmail     = "📧"
	EmojiCopy      = "📋"
	EmojiSave      = "💾"
	EmojiFolder    = "📁"
	EmojiFile      = "📄"
	EmojiKey       = "🔑"
	EmojiLock      = "🔒"
	EmojiUnlock    = "🔓"
	
	// System emojis
	EmojiComputer  = "💻"
	EmojiWindow    = "🪟"
	EmojiApple     = "🍎"
	EmojiLinux     = "🐧"
	EmojiDatabase  = "🗄️"
	EmojiRegistry  = "📋"
	EmojiProcess   = "⚡"
	
	// Browser emojis
	EmojiBrowser   = "🌐"
	EmojiChrome    = "🔍"
	EmojiLink      = "🔗"
	EmojiPage      = "📃"
	
	// Communication emojis
	EmojiPhone     = "📞"
	EmojiMessage   = "💬"
	EmojiMail      = "✉️"
	EmojiInbox     = "📥"
	EmojiOutbox    = "📤"
	
	// Progress emojis
	EmojiStep1     = "1️⃣"
	EmojiStep2     = "2️⃣"
	EmojiStep3     = "3️⃣"
	EmojiStep4     = "4️⃣"
	EmojiStep5     = "5️⃣"
	
	// Special emojis
	EmojiParty     = "🎉"
	EmojiThinking  = "🤔"
	EmojiMagic     = "✨"
	EmojiTarget    = "🎯"
	EmojiChart     = "📊"
	EmojiClock     = "🕐"
	EmojiTimer     = "⏱️"
	EmojiStopwatch = "⏱️"
)

// EmojiMap provides a map of emoji names to unicode characters
var EmojiMap = map[string]string{
	"success":   EmojiSuccess,
	"error":     EmojiError,
	"warning":   EmojiWarning,
	"info":      EmojiInfo,
	"loading":   EmojiLoading,
	"rocket":    EmojiRocket,
	"gear":      EmojiGear,
	"refresh":   EmojiRefresh,
	"email":     EmojiEmail,
	"copy":      EmojiCopy,
	"save":      EmojiSave,
	"folder":    EmojiFolder,
	"file":      EmojiFile,
	"key":       EmojiKey,
	"lock":      EmojiLock,
	"unlock":    EmojiUnlock,
	"computer":  EmojiComputer,
	"window":    EmojiWindow,
	"apple":     EmojiApple,
	"linux":     EmojiLinux,
	"database":  EmojiDatabase,
	"registry":  EmojiRegistry,
	"process":   EmojiProcess,
	"browser":   EmojiBrowser,
	"chrome":    EmojiChrome,
	"link":      EmojiLink,
	"page":      EmojiPage,
	"phone":     EmojiPhone,
	"message":   EmojiMessage,
	"mail":      EmojiMail,
	"inbox":     EmojiInbox,
	"outbox":    EmojiOutbox,
	"step1":     EmojiStep1,
	"step2":     EmojiStep2,
	"step3":     EmojiStep3,
	"step4":     EmojiStep4,
	"step5":     EmojiStep5,
	"party":     EmojiParty,
	"thinking":  EmojiThinking,
	"magic":     EmojiMagic,
	"target":    EmojiTarget,
	"chart":     EmojiChart,
	"clock":     EmojiClock,
	"timer":     EmojiTimer,
	"stopwatch": EmojiStopwatch,
}

// GetEmoji returns the emoji for the given name, or the name itself if not found
func GetEmoji(name string) string {
	if emoji, exists := EmojiMap[name]; exists {
		return emoji
	}
	return name
}

// FormatMessage formats a message with an emoji prefix
func FormatMessage(emojiName, message string) string {
	emoji := GetEmoji(emojiName)
	return emoji + " " + message
}

// FormatSuccess formats a success message
func FormatSuccess(message string) string {
	return FormatMessage("success", message)
}

// FormatError formats an error message
func FormatError(message string) string {
	return FormatMessage("error", message)
}

// FormatWarning formats a warning message
func FormatWarning(message string) string {
	return FormatMessage("warning", message)
}

// FormatInfo formats an info message
func FormatInfo(message string) string {
	return FormatMessage("info", message)
}

// FormatLoading formats a loading message
func FormatLoading(message string) string {
	return FormatMessage("loading", message)
}

// FormatStep formats a step message with step number
func FormatStep(step int, message string) string {
	var stepEmoji string
	switch step {
	case 1:
		stepEmoji = EmojiStep1
	case 2:
		stepEmoji = EmojiStep2
	case 3:
		stepEmoji = EmojiStep3
	case 4:
		stepEmoji = EmojiStep4
	case 5:
		stepEmoji = EmojiStep5
	default:
		stepEmoji = EmojiInfo
	}
	return stepEmoji + " " + message
}
