# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
bin/
dist/
build/

# Configuration files (may contain sensitive data)
config.yaml
config.yml
*.config

# Log files
*.log
logs/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp

# Screenshots
*.png
*.jpg
*.jpeg
*.gif

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Coverage files
coverage.out
coverage.html

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Package files
*.tar.gz
*.zip
*.rar

# Node.js (if using any frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if using any Python scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Local development
.local/
local/
