package email

import (
	"fmt"
	"strings"

	"cursor-manager-go/internal/config"
	"cursor-manager-go/internal/utils"
)

// Generator handles email address generation
type Generator struct {
	config *config.Config
}

// NewGenerator creates a new email generator
func NewGenerator(cfg *config.Config) *Generator {
	return &Generator{
		config: cfg,
	}
}

// GenerateRandom generates a random email address using the configured prefix
func (g *Generator) GenerateRandom() (string, error) {
	if g.config.Email.Prefix == "" {
		return "", fmt.Errorf("email prefix not configured")
	}

	domain := g.config.Email.Domain
	if domain == "" {
		domain = "2925.com"
	}

	email, err := utils.GenerateRandomEmail(g.config.Email.Prefix, domain)
	if err != nil {
		return "", fmt.Errorf("failed to generate random email: %w", err)
	}

	return email, nil
}

// GenerateWithSuffix generates an email with a specific suffix
func (g *Generator) GenerateWithSuffix(suffix string) string {
	domain := g.config.Email.Domain
	if domain == "" {
		domain = "2925.com"
	}

	return fmt.Sprintf("%s%s@%s", g.config.Email.Prefix, suffix, domain)
}

// GenerateMonitoringEmail generates the monitoring email address (fixed prefix)
func (g *Generator) GenerateMonitoringEmail() string {
	domain := g.config.Email.Domain
	if domain == "" {
		domain = "2925.com"
	}

	return fmt.Sprintf("%s@%s", g.config.Email.Prefix, domain)
}

// ValidateEmail validates an email address format
func (g *Generator) ValidateEmail(email string) error {
	if email == "" {
		return fmt.Errorf("email address cannot be empty")
	}

	if !utils.IsValidEmail(email) {
		return fmt.Errorf("invalid email format: %s", email)
	}

	// Check if it uses the configured domain
	domain := g.config.Email.Domain
	if domain == "" {
		domain = "2925.com"
	}

	if !strings.HasSuffix(email, "@"+domain) {
		return fmt.Errorf("email must use domain: %s", domain)
	}

	return nil
}

// ExtractPrefix extracts the prefix from an email address
func (g *Generator) ExtractPrefix(email string) (string, error) {
	if err := g.ValidateEmail(email); err != nil {
		return "", err
	}

	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return "", fmt.Errorf("invalid email format")
	}

	return parts[0], nil
}

// IsMonitoringEmail checks if the email is the monitoring email
func (g *Generator) IsMonitoringEmail(email string) bool {
	monitoringEmail := g.GenerateMonitoringEmail()
	return strings.EqualFold(email, monitoringEmail)
}

// GenerateBatch generates a batch of random email addresses
func (g *Generator) GenerateBatch(count int) ([]string, error) {
	if count <= 0 {
		return nil, fmt.Errorf("count must be positive")
	}

	emails := make([]string, 0, count)
	for i := 0; i < count; i++ {
		email, err := g.GenerateRandom()
		if err != nil {
			return nil, fmt.Errorf("failed to generate email %d: %w", i+1, err)
		}
		emails = append(emails, email)
	}

	return emails, nil
}

// GetDomain returns the configured email domain
func (g *Generator) GetDomain() string {
	domain := g.config.Email.Domain
	if domain == "" {
		domain = "2925.com"
	}
	return domain
}

// GetPrefix returns the configured email prefix
func (g *Generator) GetPrefix() string {
	return g.config.Email.Prefix
}

// SetPrefix updates the email prefix
func (g *Generator) SetPrefix(prefix string) {
	g.config.Email.Prefix = prefix
}

// SetDomain updates the email domain
func (g *Generator) SetDomain(domain string) {
	g.config.Email.Domain = domain
}
