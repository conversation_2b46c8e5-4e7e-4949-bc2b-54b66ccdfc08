package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// Logger wraps logrus.Logger with additional functionality
type Logger struct {
	*logrus.Logger
	callbacks []func(level logrus.Level, message string)
}

var (
	defaultLogger *Logger
)

// Config represents logger configuration
type Config struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"` // "text" or "json"
	Output     string `yaml:"output"` // "stdout", "stderr", or file path
	MaxSize    int64  `yaml:"max_size"`    // Maximum log file size in MB
	MaxBackups int    `yaml:"max_backups"` // Maximum number of backup files
	MaxAge     int    `yaml:"max_age"`     // Maximum age of log files in days
	Compress   bool   `yaml:"compress"`    // Whether to compress backup files
}

// DefaultConfig returns the default logger configuration
func DefaultConfig() *Config {
	return &Config{
		Level:      "info",
		Format:     "text",
		Output:     "stdout",
		MaxSize:    10,
		MaxBackups: 5,
		MaxAge:     30,
		Compress:   true,
	}
}

// New creates a new logger with the given configuration
func New(config *Config) (*Logger, error) {
	if config == nil {
		config = DefaultConfig()
	}

	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}
	logger.SetLevel(level)

	// Set formatter
	switch strings.ToLower(config.Format) {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	default:
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
			CallerPrettyfier: func(f *runtime.Frame) (string, string) {
				filename := filepath.Base(f.File)
				return fmt.Sprintf("%s()", f.Function), fmt.Sprintf("%s:%d", filename, f.Line)
			},
		})
	}

	// Set output
	var output io.Writer
	switch strings.ToLower(config.Output) {
	case "stdout":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// File output
		if err := os.MkdirAll(filepath.Dir(config.Output), 0755); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}
		
		file, err := os.OpenFile(config.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %w", err)
		}
		output = file
	}
	logger.SetOutput(output)

	// Enable caller reporting for better debugging
	logger.SetReportCaller(true)

	return &Logger{
		Logger:    logger,
		callbacks: make([]func(logrus.Level, string), 0),
	}, nil
}

// Init initializes the default logger
func Init(config *Config) error {
	logger, err := New(config)
	if err != nil {
		return err
	}
	defaultLogger = logger
	return nil
}

// Get returns the default logger
func Get() *Logger {
	if defaultLogger == nil {
		// Create a default logger if none exists
		logger, _ := New(DefaultConfig())
		defaultLogger = logger
	}
	return defaultLogger
}

// AddCallback adds a callback function that will be called for each log message
func (l *Logger) AddCallback(callback func(level logrus.Level, message string)) {
	l.callbacks = append(l.callbacks, callback)
}

// logWithCallback logs a message and calls all registered callbacks
func (l *Logger) logWithCallback(level logrus.Level, args ...interface{}) {
	message := fmt.Sprint(args...)
	
	// Call the original logrus method
	switch level {
	case logrus.DebugLevel:
		l.Logger.Debug(args...)
	case logrus.InfoLevel:
		l.Logger.Info(args...)
	case logrus.WarnLevel:
		l.Logger.Warn(args...)
	case logrus.ErrorLevel:
		l.Logger.Error(args...)
	case logrus.FatalLevel:
		l.Logger.Fatal(args...)
	case logrus.PanicLevel:
		l.Logger.Panic(args...)
	}
	
	// Call all callbacks
	for _, callback := range l.callbacks {
		callback(level, message)
	}
}

// logfWithCallback logs a formatted message and calls all registered callbacks
func (l *Logger) logfWithCallback(level logrus.Level, format string, args ...interface{}) {
	message := fmt.Sprintf(format, args...)
	
	// Call the original logrus method
	switch level {
	case logrus.DebugLevel:
		l.Logger.Debugf(format, args...)
	case logrus.InfoLevel:
		l.Logger.Infof(format, args...)
	case logrus.WarnLevel:
		l.Logger.Warnf(format, args...)
	case logrus.ErrorLevel:
		l.Logger.Errorf(format, args...)
	case logrus.FatalLevel:
		l.Logger.Fatalf(format, args...)
	case logrus.PanicLevel:
		l.Logger.Panicf(format, args...)
	}
	
	// Call all callbacks
	for _, callback := range l.callbacks {
		callback(level, message)
	}
}

// Override logrus methods to include callbacks

func (l *Logger) Debug(args ...interface{}) {
	l.logWithCallback(logrus.DebugLevel, args...)
}

func (l *Logger) Debugf(format string, args ...interface{}) {
	l.logfWithCallback(logrus.DebugLevel, format, args...)
}

func (l *Logger) Info(args ...interface{}) {
	l.logWithCallback(logrus.InfoLevel, args...)
}

func (l *Logger) Infof(format string, args ...interface{}) {
	l.logfWithCallback(logrus.InfoLevel, format, args...)
}

func (l *Logger) Warn(args ...interface{}) {
	l.logWithCallback(logrus.WarnLevel, args...)
}

func (l *Logger) Warnf(format string, args ...interface{}) {
	l.logfWithCallback(logrus.WarnLevel, format, args...)
}

func (l *Logger) Error(args ...interface{}) {
	l.logWithCallback(logrus.ErrorLevel, args...)
}

func (l *Logger) Errorf(format string, args ...interface{}) {
	l.logfWithCallback(logrus.ErrorLevel, format, args...)
}

func (l *Logger) Fatal(args ...interface{}) {
	l.logWithCallback(logrus.FatalLevel, args...)
}

func (l *Logger) Fatalf(format string, args ...interface{}) {
	l.logfWithCallback(logrus.FatalLevel, format, args...)
}

func (l *Logger) Panic(args ...interface{}) {
	l.logWithCallback(logrus.PanicLevel, args...)
}

func (l *Logger) Panicf(format string, args ...interface{}) {
	l.logfWithCallback(logrus.PanicLevel, format, args...)
}

// Convenience functions for the default logger

func Debug(args ...interface{}) {
	Get().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	Get().Debugf(format, args...)
}

func Info(args ...interface{}) {
	Get().Info(args...)
}

func Infof(format string, args ...interface{}) {
	Get().Infof(format, args...)
}

func Warn(args ...interface{}) {
	Get().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	Get().Warnf(format, args...)
}

func Error(args ...interface{}) {
	Get().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	Get().Errorf(format, args...)
}

func Fatal(args ...interface{}) {
	Get().Fatal(args...)
}

func Fatalf(format string, args ...interface{}) {
	Get().Fatalf(format, args...)
}

func Panic(args ...interface{}) {
	Get().Panic(args...)
}

func Panicf(format string, args ...interface{}) {
	Get().Panicf(format, args...)
}

// AddCallback adds a callback to the default logger
func AddCallback(callback func(level logrus.Level, message string)) {
	Get().AddCallback(callback)
}
