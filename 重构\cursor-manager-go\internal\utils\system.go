package utils

import (
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"syscall"
	"time"
)

// GetOS returns the current operating system
func GetOS() string {
	return runtime.GOOS
}

// GetArch returns the current architecture
func GetArch() string {
	return runtime.GOARCH
}

// GetOSInfo returns detailed OS information
func GetOSInfo() (string, string, error) {
	switch runtime.GOOS {
	case "windows":
		return getWindowsInfo()
	case "darwin":
		return getDarwinInfo()
	case "linux":
		return getLinuxInfo()
	default:
		return runtime.GOOS, "unknown", nil
	}
}

// IsAdmin checks if the current process is running with administrator privileges
func IsAdmin() bool {
	switch runtime.GOOS {
	case "windows":
		return isWindowsAdmin()
	case "darwin", "linux":
		return os.Geteuid() == 0
	default:
		return false
	}
}

// KillProcess kills a process by name
func KillProcess(processName string) error {
	switch runtime.GOOS {
	case "windows":
		return killWindowsProcess(processName)
	case "darwin", "linux":
		return killUnixProcess(processName)
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// FindProcess finds processes by name and returns their PIDs
func FindProcess(processName string) ([]int, error) {
	switch runtime.GOOS {
	case "windows":
		return findWindowsProcess(processName)
	case "darwin", "linux":
		return findUnixProcess(processName)
	default:
		return nil, fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// LaunchApplication launches an application
func LaunchApplication(appPath string, args ...string) error {
	switch runtime.GOOS {
	case "darwin":
		if strings.HasSuffix(appPath, ".app") {
			cmd := exec.Command("open", appPath)
			return cmd.Start()
		}
	}
	
	cmd := exec.Command(appPath, args...)
	return cmd.Start()
}

// FileExists checks if a file exists
func FileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// DirExists checks if a directory exists
func DirExists(path string) bool {
	info, err := os.Stat(path)
	return err == nil && info.IsDir()
}

// CreateDir creates a directory if it doesn't exist
func CreateDir(path string) error {
	if !DirExists(path) {
		return os.MkdirAll(path, 0755)
	}
	return nil
}

// getWindowsInfo returns Windows version information
func getWindowsInfo() (string, string, error) {
	cmd := exec.Command("cmd", "/c", "ver")
	output, err := cmd.Output()
	if err != nil {
		return "windows", "unknown", err
	}
	
	version := strings.TrimSpace(string(output))
	return "windows", version, nil
}

// getDarwinInfo returns macOS version information
func getDarwinInfo() (string, string, error) {
	cmd := exec.Command("sw_vers", "-productVersion")
	output, err := cmd.Output()
	if err != nil {
		return "darwin", "unknown", err
	}
	
	version := strings.TrimSpace(string(output))
	return "darwin", version, nil
}

// getLinuxInfo returns Linux distribution information
func getLinuxInfo() (string, string, error) {
	// Try to read /etc/os-release
	if data, err := os.ReadFile("/etc/os-release"); err == nil {
		lines := strings.Split(string(data), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "PRETTY_NAME=") {
				version := strings.Trim(strings.TrimPrefix(line, "PRETTY_NAME="), "\"")
				return "linux", version, nil
			}
		}
	}
	
	// Fallback to uname
	cmd := exec.Command("uname", "-a")
	output, err := cmd.Output()
	if err != nil {
		return "linux", "unknown", err
	}
	
	version := strings.TrimSpace(string(output))
	return "linux", version, nil
}

// isWindowsAdmin checks if running as administrator on Windows
func isWindowsAdmin() bool {
	cmd := exec.Command("net", "session")
	err := cmd.Run()
	return err == nil
}

// killWindowsProcess kills a process on Windows
func killWindowsProcess(processName string) error {
	cmd := exec.Command("taskkill", "/F", "/IM", processName)
	return cmd.Run()
}

// killUnixProcess kills a process on Unix-like systems
func killUnixProcess(processName string) error {
	cmd := exec.Command("pkill", "-f", processName)
	return cmd.Run()
}

// findWindowsProcess finds processes on Windows
func findWindowsProcess(processName string) ([]int, error) {
	cmd := exec.Command("tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s", processName), "/FO", "CSV")
	output, err := cmd.Output()
	if err != nil {
		return nil, err
	}
	
	var pids []int
	lines := strings.Split(string(output), "\n")
	for i, line := range lines {
		if i == 0 || strings.TrimSpace(line) == "" {
			continue // Skip header and empty lines
		}
		
		fields := strings.Split(line, ",")
		if len(fields) >= 2 {
			// Parse PID from the second field
			pidStr := strings.Trim(fields[1], "\"")
			var pid int
			if _, err := fmt.Sscanf(pidStr, "%d", &pid); err == nil {
				pids = append(pids, pid)
			}
		}
	}
	
	return pids, nil
}

// findUnixProcess finds processes on Unix-like systems
func findUnixProcess(processName string) ([]int, error) {
	cmd := exec.Command("pgrep", "-f", processName)
	output, err := cmd.Output()
	if err != nil {
		return nil, err
	}
	
	var pids []int
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}
		
		var pid int
		if _, err := fmt.Sscanf(line, "%d", &pid); err == nil {
			pids = append(pids, pid)
		}
	}
	
	return pids, nil
}

// SetProcessPriority sets the priority of the current process
func SetProcessPriority(priority int) error {
	switch runtime.GOOS {
	case "windows":
		// Windows priority classes: IDLE_PRIORITY_CLASS = 64, NORMAL_PRIORITY_CLASS = 32, etc.
		return syscall.SetPriorityClass(syscall.Handle(os.Getpid()), uint32(priority))
	case "darwin", "linux":
		// Unix nice values: -20 (highest) to 19 (lowest)
		return syscall.Setpriority(syscall.PRIO_PROCESS, 0, priority)
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// GetTimestamp returns a timestamp string for file naming
func GetTimestamp() string {
	return fmt.Sprintf("%d", time.Now().Unix())
}

// RepeatString repeats a string n times
func RepeatString(s string, n int) string {
	if n <= 0 {
		return ""
	}
	result := make([]byte, 0, len(s)*n)
	for i := 0; i < n; i++ {
		result = append(result, s...)
	}
	return string(result)
}
