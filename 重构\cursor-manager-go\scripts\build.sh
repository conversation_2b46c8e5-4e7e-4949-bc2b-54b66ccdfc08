#!/bin/bash

# Cursor Manager Go - Build Script
# This script builds the application for multiple platforms

set -e

# Configuration
APP_NAME="cursor-manager"
VERSION="1.0.0"
BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
BUILD_DIR="bin"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Go is installed
check_go() {
    if ! command -v go &> /dev/null; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    GO_VERSION=$(go version | cut -d' ' -f3)
    log_info "Using Go version: $GO_VERSION"
}

# Clean build directory
clean() {
    log_info "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"
    log_success "Build directory cleaned"
}

# Download dependencies
deps() {
    log_info "Downloading dependencies..."
    go mod download
    go mod tidy
    log_success "Dependencies downloaded"
}

# Run tests
test() {
    log_info "Running tests..."
    go test -v ./...
    log_success "Tests passed"
}

# Build for a specific platform
build_platform() {
    local GOOS=$1
    local GOARCH=$2
    local SUFFIX=$3
    
    local OUTPUT_DIR="$BUILD_DIR/$GOOS-$GOARCH"
    mkdir -p "$OUTPUT_DIR"
    
    log_info "Building for $GOOS/$GOARCH..."
    
    # Build flags
    local LDFLAGS="-X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME"
    
    # Build web version
    GOOS=$GOOS GOARCH=$GOARCH go build \
        -ldflags "$LDFLAGS" \
        -o "$OUTPUT_DIR/${APP_NAME}-web$SUFFIX" \
        ./cmd/web
    
    # Build CLI version
    GOOS=$GOOS GOARCH=$GOARCH go build \
        -ldflags "$LDFLAGS" \
        -o "$OUTPUT_DIR/${APP_NAME}-cli$SUFFIX" \
        ./cmd/cli
    
    # Build GUI version (placeholder)
    GOOS=$GOOS GOARCH=$GOARCH go build \
        -ldflags "$LDFLAGS" \
        -o "$OUTPUT_DIR/${APP_NAME}-gui$SUFFIX" \
        ./cmd/gui
    
    log_success "Built for $GOOS/$GOARCH"
}

# Build for current platform
build_current() {
    log_info "Building for current platform..."
    
    local GOOS=$(go env GOOS)
    local GOARCH=$(go env GOARCH)
    local SUFFIX=""
    
    if [ "$GOOS" = "windows" ]; then
        SUFFIX=".exe"
    fi
    
    # Build flags
    local LDFLAGS="-X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME"
    
    # Build web version
    go build -ldflags "$LDFLAGS" -o "$BUILD_DIR/${APP_NAME}-web$SUFFIX" ./cmd/web
    
    # Build CLI version
    go build -ldflags "$LDFLAGS" -o "$BUILD_DIR/${APP_NAME}-cli$SUFFIX" ./cmd/cli
    
    # Build GUI version (placeholder)
    go build -ldflags "$LDFLAGS" -o "$BUILD_DIR/${APP_NAME}-gui$SUFFIX" ./cmd/gui
    
    log_success "Built for current platform ($GOOS/$GOARCH)"
}

# Build for all platforms
build_all() {
    log_info "Building for all platforms..."
    
    # Windows
    build_platform "windows" "amd64" ".exe"
    build_platform "windows" "386" ".exe"
    
    # macOS
    build_platform "darwin" "amd64" ""
    build_platform "darwin" "arm64" ""
    
    # Linux
    build_platform "linux" "amd64" ""
    build_platform "linux" "386" ""
    build_platform "linux" "arm64" ""
    build_platform "linux" "arm" ""
    
    log_success "Built for all platforms"
}

# Create release packages
package() {
    log_info "Creating release packages..."
    
    local PACKAGE_DIR="$BUILD_DIR/packages"
    mkdir -p "$PACKAGE_DIR"
    
    # Package for each platform
    for platform_dir in "$BUILD_DIR"/*-*/; do
        if [ -d "$platform_dir" ]; then
            local platform=$(basename "$platform_dir")
            local archive_name="${APP_NAME}-${VERSION}-${platform}"
            
            log_info "Packaging $platform..."
            
            # Copy files to temporary directory
            local temp_dir=$(mktemp -d)
            local package_content="$temp_dir/$archive_name"
            mkdir -p "$package_content"
            
            # Copy binaries
            cp "$platform_dir"/* "$package_content/"
            
            # Copy documentation
            cp README.md "$package_content/" 2>/dev/null || true
            cp LICENSE "$package_content/" 2>/dev/null || true
            
            # Create archive
            if [[ "$platform" == *"windows"* ]]; then
                # Create ZIP for Windows
                (cd "$temp_dir" && zip -r "$PACKAGE_DIR/${archive_name}.zip" "$archive_name")
            else
                # Create tar.gz for Unix-like systems
                (cd "$temp_dir" && tar -czf "$PACKAGE_DIR/${archive_name}.tar.gz" "$archive_name")
            fi
            
            # Cleanup
            rm -rf "$temp_dir"
            
            log_success "Packaged $platform"
        fi
    done
    
    log_success "Release packages created in $PACKAGE_DIR"
}

# Show build information
info() {
    echo "Cursor Manager Go - Build Information"
    echo "====================================="
    echo "Version: $VERSION"
    echo "Build Time: $BUILD_TIME"
    echo "Build Directory: $BUILD_DIR"
    echo ""
    echo "Available commands:"
    echo "  clean     - Clean build directory"
    echo "  deps      - Download dependencies"
    echo "  test      - Run tests"
    echo "  build     - Build for current platform"
    echo "  build-all - Build for all platforms"
    echo "  package   - Create release packages"
    echo "  install   - Install to system"
    echo "  dev       - Start development server"
    echo ""
}

# Install to system
install() {
    log_info "Installing to system..."
    
    if [ ! -f "$BUILD_DIR/${APP_NAME}-web" ] && [ ! -f "$BUILD_DIR/${APP_NAME}-web.exe" ]; then
        log_error "Binaries not found. Please run 'build' first."
        exit 1
    fi
    
    # Determine install directory
    local INSTALL_DIR="/usr/local/bin"
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
        INSTALL_DIR="/usr/bin"
    fi
    
    # Copy binaries
    sudo cp "$BUILD_DIR/${APP_NAME}"-* "$INSTALL_DIR/" 2>/dev/null || {
        log_warning "Failed to install to $INSTALL_DIR, trying without sudo..."
        cp "$BUILD_DIR/${APP_NAME}"-* "$INSTALL_DIR/" 2>/dev/null || {
            log_error "Installation failed. Please check permissions."
            exit 1
        }
    }
    
    log_success "Installed to $INSTALL_DIR"
}

# Development server
dev() {
    log_info "Starting development server..."
    go run ./cmd/web --debug --port 8080
}

# Main script logic
main() {
    check_go
    
    case "${1:-build}" in
        "clean")
            clean
            ;;
        "deps")
            deps
            ;;
        "test")
            test
            ;;
        "build")
            clean
            deps
            build_current
            ;;
        "build-all")
            clean
            deps
            build_all
            ;;
        "package")
            package
            ;;
        "install")
            install
            ;;
        "dev")
            dev
            ;;
        "info")
            info
            ;;
        "help"|"--help"|"-h")
            info
            ;;
        *)
            log_error "Unknown command: $1"
            info
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
