# 续杯工具 - 纯本地界面版本使用说明

## 🌟 版本特点

这是续杯工具的纯本地界面版本，具有以下特点：

- ✅ **无需服务器**: 不依赖Flask或其他Web服务器
- ✅ **完全本地**: 使用本地HTML文件和pywebview的JS API
- ✅ **现代界面**: 美观的HTML/CSS界面
- ✅ **快速启动**: 无需等待服务器启动
- ✅ **稳定可靠**: 避免了网络相关的问题
- ✅ **无弹窗**: 不会出现127.0.0.1安全弹窗

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动应用
```bash
python 启动本地界面.py
```

### 3. 开始使用
应用启动后会显示一个现代化的界面窗口，包含以下功能区域：

- **一键登录工具**: 生成随机邮箱并执行自动登录
- **邮箱凭据设置**: 配置邮箱前缀和密码
- **浏览器设置**: 设置Chrome浏览器路径
- **系统工具**: 环境重置和邮箱客户端
- **运行状态**: 实时显示操作状态和日志

## 📋 功能说明

### 一键登录工具
1. **当前邮箱**: 显示自动生成的随机邮箱地址
2. **复制按钮**: 点击复制邮箱到剪贴板
3. **一键登录**: 使用当前邮箱执行自动登录流程

### 邮箱凭据设置
1. **邮箱前缀**: 输入您的邮箱前缀（不包含@2925.com）
2. **邮箱密码**: 输入邮箱密码
3. **保存凭据**: 保存设置到配置文件

### 浏览器设置
1. **Chrome路径**: 输入Chrome浏览器的完整路径
2. **保存路径**: 保存浏览器路径到配置文件

### 系统工具
1. **一键重置环境**: 关闭Cursor进程并重置机器ID
2. **打开邮箱客户端**: 启动邮件监控，自动获取验证码

## 🔧 技术架构

### 前端
- **HTML/CSS/JavaScript**: 自包含的单文件界面
- **响应式设计**: 适配不同屏幕尺寸
- **现代化样式**: 渐变背景、卡片布局、动画效果

### 后端
- **pywebview**: 创建本地应用窗口
- **JS API**: 通过pywebview的JS API与Python后端通信
- **本地界面管理器**: 提供所有业务功能的API接口

### 通信机制
```
HTML界面 ←→ pywebview JS API ←→ Python后端
```

## 🛠️ 开发说明

### 文件结构
```
本地界面/
├── 本地界面管理器.py    # Python后端API
├── index.html          # HTML界面文件
└── 使用说明.md         # 本文件
```

### API接口
本地界面管理器提供以下API接口：

- `get_config()`: 获取配置信息
- `save_config(data)`: 保存配置信息
- `generate_email()`: 生成随机邮箱
- `auto_login(data)`: 执行一键登录
- `reset_environment()`: 重置环境
- `open_email_client()`: 打开邮箱客户端

### 前端调用示例
```javascript
// 获取配置
const config = await window.pywebview.api.get_config();

// 保存配置
const result = await window.pywebview.api.save_config({
    email_prefix: 'test',
    email_password: 'password'
});

// 生成邮箱
const email = await window.pywebview.api.generate_email();
```

## 🔍 故障排除

### 常见问题

1. **界面无法启动**
   - 检查是否安装了pywebview: `pip install pywebview`
   - 确保本地界面文件存在

2. **功能无法使用**
   - 检查核心模块是否正常导入
   - 查看控制台错误信息

3. **配置无法保存**
   - 检查文件权限
   - 确保配置文件路径可写

### 调试模式
如果需要调试，可以修改启动器中的debug参数：
```python
webview.start(debug=True)  # 启用调试模式
```

## 🎯 优势对比

| 特性 | 纯本地版本 | Web服务器版本 | Tkinter版本 |
|------|------------|---------------|-------------|
| 启动速度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 界面美观 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 稳定性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 资源占用 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 无弹窗 | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |

## 📞 技术支持

如果遇到问题：
1. 查看控制台输出信息
2. 检查error.log文件
3. 确认依赖是否正确安装
4. 尝试重新启动应用

## 🎉 享受纯本地体验！

这个版本结合了现代化界面的美观和本地应用的稳定性，为您提供最佳的用户体验！
