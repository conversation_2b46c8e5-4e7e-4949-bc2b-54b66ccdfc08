<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow: auto; /* 允许滚动，以防内容超出窗口 */
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            padding: 6px;
            height: 100vh;
            box-sizing: border-box;
            min-width: 800px; /* 确保最小宽度，防止布局破坏 */
        }

        .left-panel {
            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: repeat(4, auto);
            gap: 6px;
            height: 100%;
            align-content: start;
            min-width: 0; /* 防止内容溢出 */
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 6px;
            padding: 10px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: auto;
            min-height: 120px;
            min-width: 0; /* 防止内容溢出 */
            overflow: hidden; /* 确保内容不会溢出卡片 */
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 1em;
            font-weight: 600;
            margin-bottom: 6px;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .card-title::before {
            content: "🚀";
            font-size: 1.2em;
        }

        .card-title.email::before { content: "📧"; }
        .card-title.browser::before { content: "🌐"; }
        .card-title.tools::before { content: "🔧"; }

        .form-group {
            margin-bottom: 5px;
            flex: 1;
        }

        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .card-actions {
            margin-top: 6px;
            padding-top: 4px;
        }

        .form-label {
            display: block;
            margin-bottom: 3px;
            font-weight: 500;
            color: #4a5568;
            font-size: 12px;
        }

        .form-input {
            width: 100%;
            padding: 6px 8px;
            border: 2px solid #e2e8f0;
            border-radius: 4px;
            font-size: 11px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box; /* 确保padding不会增加总宽度 */
            min-width: 0; /* 允许收缩 */
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            text-decoration: none;
            justify-content: center;
            width: 100%;
            margin-bottom: 3px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(237, 137, 54, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .input-group {
            display: flex;
            gap: 5px;
            align-items: end;
        }

        .input-group .form-input {
            flex: 1;
        }

        .status-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 6px;
            padding: 10px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-width: 0; /* 防止内容溢出 */
        }

        .status-header {
            font-size: 1em;
            font-weight: 600;
            margin-bottom: 6px;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-header::before {
            content: "💻";
            font-size: 1.2em;
        }

        .status-content {
            flex: 1;
            overflow-y: auto;
            background: #f7fafc;
            border-radius: 4px;
            padding: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
            line-height: 1.3;
        }

        .status-message {
            margin-bottom: 4px;
            padding: 5px 8px;
            border-radius: 4px;
            background: white;
            border-left: 3px solid #667eea;
            animation: slideIn 0.3s ease;
        }

        .status-message.error {
            border-left-color: #e53e3e;
            background: #fed7d7;
        }

        .status-message.success {
            border-left-color: #38a169;
            background: #c6f6d5;
        }

        .status-message.warning {
            border-left-color: #dd6b20;
            background: #feebc8;
        }

        .status-timestamp {
            color: #718096;
            font-size: 11px;
            margin-right: 8px;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideInRight 0.3s ease;
        }

        .notification.success {
            background: #38a169;
        }

        .notification.error {
            background: #e53e3e;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 移除响应式布局，保持固定的左右分栏布局 */
    </style>
</head>
<body>
    <div class="container">
        <div class="left-panel">
            <!-- 一键登录工具 -->
            <div class="card">
                <div class="card-title">
                    一键登录工具
                </div>
                <div class="card-content">
                    <div class="form-group">
                        <label class="form-label">当前邮箱</label>
                        <div class="input-group">
                            <input type="text" id="currentEmail" class="form-input" readonly>
                            <button class="btn btn-secondary" onclick="copyEmail()" style="width: auto; margin: 0;">
                                📋 复制
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-primary" onclick="autoLogin()">
                        🚀 一键登录 (当前页面)
                    </button>
                </div>
            </div>

            <!-- 邮箱凭据设置 -->
            <div class="card">
                <div class="card-title email">
                    邮箱凭据设置
                </div>
                <div class="card-content">
                    <div class="form-group">
                        <label class="form-label">邮箱前缀</label>
                        <input type="text" id="emailPrefix" class="form-input" placeholder="输入邮箱前缀">
                    </div>
                    <div class="form-group">
                        <label class="form-label">邮箱密码</label>
                        <input type="password" id="emailPassword" class="form-input" placeholder="输入邮箱密码">
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-success" onclick="saveCredentials()">
                        💾 保存凭据
                    </button>
                </div>
            </div>

            <!-- 浏览器设置 -->
            <div class="card">
                <div class="card-title browser">
                    浏览器设置
                </div>
                <div class="card-content">
                    <div class="form-group">
                        <label class="form-label">Chrome路径</label>
                        <input type="text" id="browserPath" class="form-input" placeholder="Chrome浏览器路径">
                    </div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-success" onclick="saveBrowserPath()">
                        💾 保存路径
                    </button>
                </div>
            </div>

            <!-- 系统工具 -->
            <div class="card">
                <div class="card-title tools">
                    系统工具
                </div>
                <div class="card-content">
                    <!-- 占位内容，保持卡片高度一致 -->
                    <div style="flex: 1;"></div>
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="resetEnvironment()">
                        🔄 一键重置环境
                    </button>
                    <button class="btn btn-primary" onclick="openEmailClient()">
                        📧 打开邮箱客户端
                    </button>
                </div>
            </div>
        </div>

        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-header">
                运行状态
            </div>
            <div class="status-content" id="statusContent">
                <div class="status-message">
                    <span class="status-timestamp">00:00:00</span>
                    准备就绪。请点击按钮开始。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let isLoading = false;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 等待pywebview API准备就绪
            if (window.pywebview) {
                initializeApp();
            } else {
                // 如果pywebview还没准备好，等待pywebviewready事件
                window.addEventListener('pywebviewready', initializeApp);
            }
        });

        function initializeApp() {
            addStatusMessage('界面初始化完成');
            loadConfig();
            generateEmail();
        }

        // 添加状态消息
        function addStatusMessage(message, type = 'info') {
            const statusContent = document.getElementById('statusContent');
            const timestamp = new Date().toLocaleTimeString();
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-message ${type}`;
            messageDiv.innerHTML = `<span class="status-timestamp">${timestamp}</span>${message}`;
            
            statusContent.appendChild(messageDiv);
            statusContent.scrollTop = statusContent.scrollHeight;
        }

        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 加载配置
        async function loadConfig() {
            try {
                if (window.pywebview && window.pywebview.api) {
                    const config = await window.pywebview.api.get_config();
                    if (config.success) {
                        document.getElementById('emailPrefix').value = config.data.email_prefix || '';
                        document.getElementById('emailPassword').value = config.data.email_password || '';
                        document.getElementById('browserPath').value = config.data.browser_path || '';
                        addStatusMessage('配置加载成功', 'success');
                    } else {
                        addStatusMessage('配置加载失败: ' + config.error, 'error');
                    }
                }
            } catch (error) {
                addStatusMessage('加载配置时出错: ' + error.message, 'error');
            }
        }

        // 生成随机邮箱
        async function generateEmail() {
            try {
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.generate_email();
                    if (result.success) {
                        document.getElementById('currentEmail').value = result.email;
                        addStatusMessage('邮箱生成成功: ' + result.email, 'success');
                    } else {
                        addStatusMessage('邮箱生成失败: ' + result.error, 'error');
                    }
                }
            } catch (error) {
                addStatusMessage('生成邮箱时出错: ' + error.message, 'error');
            }
        }

        // 复制邮箱
        async function copyEmail() {
            const email = document.getElementById('currentEmail').value;
            if (email) {
                try {
                    await navigator.clipboard.writeText(email);
                    showNotification('邮箱已复制到剪贴板', 'success');
                    addStatusMessage('邮箱已复制: ' + email, 'success');
                } catch (error) {
                    // 备用复制方法
                    const textArea = document.createElement('textarea');
                    textArea.value = email;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showNotification('邮箱已复制到剪贴板', 'success');
                    addStatusMessage('邮箱已复制: ' + email, 'success');
                }
            }
        }

        // 保存凭据
        async function saveCredentials() {
            if (isLoading) return;
            
            const emailPrefix = document.getElementById('emailPrefix').value.trim();
            const emailPassword = document.getElementById('emailPassword').value;
            
            if (!emailPrefix || !emailPassword) {
                showNotification('请填写完整的邮箱凭据', 'error');
                return;
            }
            
            try {
                isLoading = true;
                addStatusMessage('正在保存凭据...');
                
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.save_config({
                        email_prefix: emailPrefix,
                        email_password: emailPassword
                    });
                    
                    if (result.success) {
                        showNotification('凭据保存成功', 'success');
                        addStatusMessage('凭据保存成功', 'success');
                        // 重新生成邮箱
                        generateEmail();
                    } else {
                        showNotification('凭据保存失败: ' + result.error, 'error');
                        addStatusMessage('凭据保存失败: ' + result.error, 'error');
                    }
                }
            } catch (error) {
                showNotification('保存凭据时出错: ' + error.message, 'error');
                addStatusMessage('保存凭据时出错: ' + error.message, 'error');
            } finally {
                isLoading = false;
            }
        }

        // 保存浏览器路径
        async function saveBrowserPath() {
            if (isLoading) return;
            
            const browserPath = document.getElementById('browserPath').value.trim();
            
            if (!browserPath) {
                showNotification('请输入浏览器路径', 'error');
                return;
            }
            
            try {
                isLoading = true;
                addStatusMessage('正在保存浏览器路径...');
                
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.save_config({
                        browser_path: browserPath
                    });
                    
                    if (result.success) {
                        showNotification('浏览器路径保存成功', 'success');
                        addStatusMessage('浏览器路径保存成功', 'success');
                    } else {
                        showNotification('浏览器路径保存失败: ' + result.error, 'error');
                        addStatusMessage('浏览器路径保存失败: ' + result.error, 'error');
                    }
                }
            } catch (error) {
                showNotification('保存浏览器路径时出错: ' + error.message, 'error');
                addStatusMessage('保存浏览器路径时出错: ' + error.message, 'error');
            } finally {
                isLoading = false;
            }
        }

        // 一键登录
        async function autoLogin() {
            if (isLoading) return;
            
            const loginEmail = document.getElementById('currentEmail').value;
            
            if (!loginEmail) {
                showNotification('请先生成邮箱', 'error');
                return;
            }
            
            try {
                isLoading = true;
                addStatusMessage('正在启动一键登录...');
                
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.auto_login({
                        login_email: loginEmail
                    });
                    
                    if (result.success) {
                        addStatusMessage('一键登录流程已启动', 'success');
                    } else {
                        showNotification('一键登录启动失败: ' + result.error, 'error');
                        addStatusMessage('一键登录启动失败: ' + result.error, 'error');
                    }
                }
            } catch (error) {
                showNotification('一键登录时出错: ' + error.message, 'error');
                addStatusMessage('一键登录时出错: ' + error.message, 'error');
            } finally {
                isLoading = false;
            }
        }

        // 重置环境
        async function resetEnvironment() {
            if (isLoading) return;
            
            if (!confirm('确定要重置环境吗？这将关闭Cursor并重置相关设置。')) {
                return;
            }
            
            try {
                isLoading = true;
                addStatusMessage('正在重置环境...');
                
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.reset_environment();
                    
                    if (result.success) {
                        addStatusMessage('环境重置流程已启动', 'success');
                    } else {
                        showNotification('环境重置启动失败: ' + result.error, 'error');
                        addStatusMessage('环境重置启动失败: ' + result.error, 'error');
                    }
                }
            } catch (error) {
                showNotification('重置环境时出错: ' + error.message, 'error');
                addStatusMessage('重置环境时出错: ' + error.message, 'error');
            } finally {
                isLoading = false;
            }
        }

        // 打开邮箱客户端
        async function openEmailClient() {
            if (isLoading) return;
            
            try {
                isLoading = true;
                addStatusMessage('正在打开邮箱客户端...');
                
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.open_email_client();
                    
                    if (result.success) {
                        addStatusMessage('邮箱客户端已启动', 'success');
                    } else {
                        showNotification('邮箱客户端启动失败: ' + result.error, 'error');
                        addStatusMessage('邮箱客户端启动失败: ' + result.error, 'error');
                    }
                }
            } catch (error) {
                showNotification('打开邮箱客户端时出错: ' + error.message, 'error');
                addStatusMessage('打开邮箱客户端时出错: ' + error.message, 'error');
            } finally {
                isLoading = false;
            }
        }

        // 定期获取状态消息
        async function pollStatusMessages() {
            try {
                if (window.pywebview && window.pywebview.api) {
                    const result = await window.pywebview.api.get_status_messages();
                    if (result.success && result.messages && result.messages.length > 0) {
                        result.messages.forEach(msg => {
                            // 根据消息内容判断类型
                            let type = 'info';
                            if (msg.message.includes('错误') || msg.message.includes('❌') || msg.message.includes('失败')) {
                                type = 'error';
                            } else if (msg.message.includes('成功') || msg.message.includes('✅') || msg.message.includes('完成')) {
                                type = 'success';
                            } else if (msg.message.includes('警告') || msg.message.includes('⚠️')) {
                                type = 'warning';
                            }

                            // 使用后端提供的时间戳
                            const statusContent = document.getElementById('statusContent');
                            const messageDiv = document.createElement('div');
                            messageDiv.className = `status-message ${type}`;
                            messageDiv.innerHTML = `<span class="status-timestamp">${msg.timestamp}</span>${msg.message}`;

                            statusContent.appendChild(messageDiv);
                            statusContent.scrollTop = statusContent.scrollHeight;
                        });
                    }
                }
            } catch (error) {
                console.error('获取状态消息时出错:', error);
            }
        }

        // 启动状态消息轮询
        setInterval(pollStatusMessages, 500); // 每500毫秒检查一次状态消息

        // 定期刷新邮箱
        setInterval(() => {
            if (!isLoading) {
                generateEmail();
            }
        }, 30000); // 每30秒刷新一次
    </script>
</body>
</html>
